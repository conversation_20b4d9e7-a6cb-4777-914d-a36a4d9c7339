import socket
import subprocess
import time
import logging
import os
import sys
import psutil
from datetime import datetime

time_stamp = datetime.now().strftime('%Y%m%d_%H%M%S')
os.makedirs("logs", exist_ok=True)

logging.basicConfig(
    filename=f'logs/port_monitor_{time_stamp}.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filemode='w'
)

TARGET_PORT = 8000
START_COMMAND = ['python', '-u', 'start_zhoushan_wildboar.py']  # -u 禁用输出缓冲
# START_COMMAND = ['python', '-u', '-c', 'import obfuscated; obfuscated.run()']
CONDA_ENV_NAME = 'torch1'
MEMORY_LIMIT = 50 * 1024**3 # GB内存限制
COOLDOWN = 30             # 进程崩溃后冷却时间(秒)

# 全局状态
process = None
restart_count = 0
last_start_time = 0

def get_process_tree_memory(proc):
    """获取进程及其子进程的总内存"""
    try:
        parent = psutil.Process(proc.pid)
        children = parent.children(recursive=True)
        total_mem = parent.memory_info().rss
        for child in children:
            total_mem += child.memory_info().rss
        return total_mem
    except psutil.NoSuchProcess:
        return 0

def monitor_resources():
    """资源监控"""
    global process
    if not process:
        return False
    
    try:
        # 检测总内存使用
        total_mem = get_process_tree_memory(process)
        if total_mem > MEMORY_LIMIT:
            logging.warning(f"Memory overflow: {total_mem//1024**2}MB")
            return True
        
        # 检测CPU占用率（10秒内平均值）
        cpu_percent = psutil.cpu_percent(interval=10)
        if cpu_percent > 90:  # CPU超过90%
            logging.warning(f"CPU overload: {cpu_percent}%")
            return True
            
    except Exception as e:
        logging.error(f"Resource monitoring failed: {e}")
    
    return False

def safe_terminate():
    """安全终止进程树"""
    global process
    if process and process.poll() is None:
        try:
            parent = psutil.Process(process.pid)
            children = parent.children(recursive=True)
            for child in children:
                child.terminate()
            parent.terminate()
            process.wait(timeout=10)
        except (psutil.NoSuchProcess, subprocess.TimeoutExpired):
            pass
        finally:
            process = None

def start_process():
    """启动目标进程（带conda环境）"""
    global process, restart_count, last_start_time
    
    # 冷却时间检查
    if time.time() - last_start_time < COOLDOWN:
        return
    
    # 进程存活检查
    if process and process.poll() is None:
        logging.info("Process already running")
        return
    
    try:
        safe_terminate()
        
        # 构造conda命令
        full_command = [
            'conda', 'run', '--no-capture-output',
            '-n', CONDA_ENV_NAME
        ] + START_COMMAND
        
        logging.info(f"Starting process: {full_command}")
        process = subprocess.Popen(
            full_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        # 启动异步日志记录
        def log_output():
            while process.poll() is None:
                output = process.stdout.readline()
                if output:
                    logging.info(f"[PROCESS] {output.strip()}")
        
        import threading
        threading.Thread(target=log_output, daemon=True).start()
        
        last_start_time = time.time()
        restart_count += 1
        
    except Exception as e:
        logging.error(f"Process start failed: {e}")
        sys.exit(1)

def is_port_in_use(port):
    """检测端口占用状态"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.settimeout(2)
        return s.connect_ex(('localhost', port)) == 0

def main():
    global restart_count
    
    logging.info("===== Port Monitor V2 Started =====")
    
    try:
        while True:
            # # 资源监控
            # if monitor_resources():
            #     logging.warning("Resource limit exceeded, terminating...")
            #     safe_terminate()
            #     time.sleep(COOLDOWN)
            #     continue
                
            # 端口检测逻辑
            if is_port_in_use(TARGET_PORT):
                logging.info(f"Port {TARGET_PORT} in use, waiting...")
                time.sleep(30)  # 长等待周期
            else:
                logging.info(f"Port {TARGET_PORT} available")
                start_process()
                time.sleep(10)  # 短检测周期
                
    except KeyboardInterrupt:
        logging.info("Received keyboard interrupt")
    finally:
        safe_terminate()
        logging.info("===== Service Stopped =====")
        sys.exit(0)

if __name__ == "__main__":
    main()
