#!/usr/bin/env python3
"""
优化版鸟类识别服务启动脚本
提供服务启动、监控和管理功能
"""

import os
import sys
import time
import signal
import argparse
import subprocess
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ServiceManager:
    """服务管理器"""
    
    def __init__(self, config_file='config_zhoushan_optimized.yaml'):
        self.config_file = config_file
        self.process = None
        self.running = False
        
    def start_service(self, debug=False):
        """启动服务"""
        try:
            # 检查依赖
            self._check_dependencies()
            
            # 创建必要目录
            self._create_directories()
            
            # 启动服务
            cmd = [sys.executable, 'deploy_zhoushan_optimized.py']
            if debug:
                cmd.append('--debug')
                
            logger.info(f"Starting service with command: {' '.join(cmd)}")
            
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.running = True
            
            # 监控服务输出
            self._monitor_service()
            
        except Exception as e:
            logger.error(f"Failed to start service: {e}")
            raise
            
    def stop_service(self):
        """停止服务"""
        if self.process and self.running:
            logger.info("Stopping service...")
            self.process.terminate()
            
            # 等待进程结束
            try:
                self.process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning("Service didn't stop gracefully, forcing...")
                self.process.kill()
                
            self.running = False
            logger.info("Service stopped")
            
    def restart_service(self):
        """重启服务"""
        logger.info("Restarting service...")
        self.stop_service()
        time.sleep(2)
        self.start_service()
        
    def _check_dependencies(self):
        """检查依赖"""
        required_files = [
            'deploy_zhoushan_optimized.py',
            self.config_file,
            'categories.py'
        ]
        
        for file in required_files:
            if not os.path.exists(file):
                raise FileNotFoundError(f"Required file not found: {file}")
                
        # 检查模型文件
        model_dirs = [
            'filedata/AiModel/model',
            'filedata/AiModel/tmp'
        ]
        
        for dir_path in model_dirs:
            if not os.path.exists(dir_path):
                logger.warning(f"Model directory not found: {dir_path}")
                
    def _create_directories(self):
        """创建必要目录"""
        directories = [
            'logs',
            'filedata/AiModel/tmp',
            'filedata/AiModel/image_reco_output'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            
    def _monitor_service(self):
        """监控服务"""
        logger.info("Service started, monitoring output...")
        
        try:
            while self.running and self.process.poll() is None:
                output = self.process.stdout.readline()
                if output:
                    print(output.strip())
                    
        except KeyboardInterrupt:
            logger.info("Received interrupt signal")
            self.stop_service()
            
        except Exception as e:
            logger.error(f"Service monitoring error: {e}")
            
        finally:
            if self.process and self.process.poll() is None:
                self.stop_service()

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"Received signal {signum}")
    sys.exit(0)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='优化版鸟类识别服务管理器')
    parser.add_argument('action', choices=['start', 'stop', 'restart', 'status'], 
                       help='服务操作')
    parser.add_argument('--config', default='config_zhoushan_optimized.yaml',
                       help='配置文件路径')
    parser.add_argument('--debug', action='store_true',
                       help='调试模式')
    parser.add_argument('--daemon', action='store_true',
                       help='后台运行')
    
    args = parser.parse_args()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    manager = ServiceManager(args.config)
    
    try:
        if args.action == 'start':
            if args.daemon:
                # 后台运行模式
                import daemon
                with daemon.DaemonContext():
                    manager.start_service(args.debug)
            else:
                manager.start_service(args.debug)
                
        elif args.action == 'stop':
            manager.stop_service()
            
        elif args.action == 'restart':
            manager.restart_service()
            
        elif args.action == 'status':
            # 检查服务状态
            import requests
            try:
                response = requests.get('http://localhost:5000/health', timeout=5)
                if response.status_code == 200:
                    print("Service is running")
                    print(f"Status: {response.json()}")
                else:
                    print(f"Service returned status code: {response.status_code}")
            except requests.exceptions.RequestException:
                print("Service is not running or not accessible")
                
    except Exception as e:
        logger.error(f"Operation failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
