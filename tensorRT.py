
from ultralytics import YOLO
import time
trt_model_path = "E:/python/接口参考和模型/模型/yolov12/best.engine"
model = YOLO(trt_model_path, task="detect")
def tensorRT(img_path):
      results = model(img_path,
                  verbose=False,
                  conf=0.5,
                  half=True,
                  save=False,
                  show=False,
                )

      # 处理结果
      result = results[0]
      bboxes = result.boxes.xyxy.cpu().numpy().tolist() if result.boxes is not None else []
      labels = result.boxes.cls.cpu().numpy().tolist() if result.boxes is not None else []
      scores = result.boxes.conf.cpu().numpy().tolist() if result.boxes is not None else []

      class_names = model.names
      classes = [class_names[int(label)] for label in labels]
      print(bboxes, labels, scores, classes)
timestamp = time.time()
tensorRT("E:/assets/image/鸟类/img/bage/八哥.jpg")
timestamp = time.time()
print(timestamp)