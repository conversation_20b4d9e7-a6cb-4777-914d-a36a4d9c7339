#!/usr/bin/env python3
"""
高性能鸟类识别服务 - 优化版本
作者: AI Assistant
版本: 2.0
特性: 高性能、稳健、模块化设计
"""

import os
import sys
import time
import uuid
import json
import logging
import asyncio
import threading
import gc
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple, Union
from contextlib import contextmanager
from functools import wraps, lru_cache
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field
from collections import defaultdict, deque
import queue

# 第三方库
import cv2
import yaml
import torch
import librosa
import requests
import numpy as np
import psutil
import base64
import tempfile
import shutil
import subprocess
import shlex
import signal
from PIL import Image, ImageDraw, ImageFont
from minio import Minio
from minio.error import S3Error
from ultralytics import YOLO
from transformers import pipeline
from flask import Flask, request, jsonify, Response
from flask_cors import CORS
from gevent import pywsgi
from werkzeug.utils import secure_filename

# 本地模块
from categories import *

# 导入轮转调度器
from camera_rotation_scheduler_zhoushan import CameraRotationScheduler, RotationConfig


# 移除复杂的FFmpeg类，回归简单的OpenCV实现


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 全局配置
@dataclass
class ServiceConfig:
    """服务配置类"""
    max_workers: int = 8
    queue_size: int = 1000
    memory_limit_mb: int = 4096
    memory_check_interval: float = 30.0
    request_timeout: int = 300
    retry_attempts: int = 3
    retry_delay: float = 1.0

    # 模型配置
    model_device: str = "cuda:0"
    batch_size: int = 4

    # 缓存配置
    cache_size: int = 128
    cache_ttl: int = 3600

    # 独立处理配置
    audio_workers: int = 2  # 声纹处理工作线程数
    image_workers: int = 3  # 图像处理工作线程数
    video_workers: int = 2  # 视频处理工作线程数
    stream_workers: int = 1  # 流处理工作线程数

@dataclass
class JobResult:
    """任务结果类"""
    job_id: str
    status: str = "queued"
    progress: str = "0/0"
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    result: Optional[Dict] = None
    error: Optional[str] = None
    cancelled: bool = False
    estimated_remaining_time: str = ""

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = defaultdict(list)
        self.start_time = time.time()
        
    def record_metric(self, name: str, value: float):
        """记录性能指标"""
        self.metrics[name].append({
            'timestamp': time.time(),
            'value': value
        })
        
    def get_average(self, name: str, window_seconds: int = 300) -> float:
        """获取指定时间窗口内的平均值"""
        current_time = time.time()
        cutoff_time = current_time - window_seconds
        
        recent_values = [
            m['value'] for m in self.metrics[name]
            if m['timestamp'] > cutoff_time
        ]
        
        return sum(recent_values) / len(recent_values) if recent_values else 0.0
        
    def get_system_metrics(self) -> Dict[str, float]:
        """获取系统指标"""
        process = psutil.Process()
        return {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'memory_mb': process.memory_info().rss / 1024 / 1024,
            'disk_usage': psutil.disk_usage('/').percent,
            'uptime': time.time() - self.start_time
        }

class ResourceManager:
    """资源管理器"""

    def __init__(self, config: ServiceConfig):
        self.config = config
        self.executor = ThreadPoolExecutor(max_workers=config.max_workers)
        self.model_cache = {}
        self.connection_pool = {}
        self.max_cache_size = config.cache_size  # 限制缓存大小
        
    @contextmanager
    def get_model(self, model_type: str):
        """获取模型实例（带缓存）"""
        if model_type not in self.model_cache:
            # 检查缓存大小限制
            if len(self.model_cache) >= self.max_cache_size:
                # 移除最旧的模型
                oldest_key = next(iter(self.model_cache))
                old_model = self.model_cache.pop(oldest_key)
                if hasattr(old_model, 'cleanup'):
                    old_model.cleanup()
                logger.info(f"Removed old model from cache: {oldest_key}")

            logger.info(f"Loading model: {model_type}")
            # 这里会在后续实现具体的模型加载逻辑
            pass

        try:
            yield self.model_cache.get(model_type)
        finally:
            # 清理逻辑（如果需要）
            pass
            
    def cleanup(self):
        """清理资源"""
        self.executor.shutdown(wait=True)
        for model in self.model_cache.values():
            if hasattr(model, 'cleanup'):
                model.cleanup()
        self.model_cache.clear()
        self.connection_pool.clear()

class RetryHandler:
    """重试处理器"""
    
    @staticmethod
    def with_retry(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0):
        """重试装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                last_exception = None
                
                for attempt in range(max_attempts):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        last_exception = e
                        if attempt < max_attempts - 1:
                            wait_time = delay * (backoff ** attempt)
                            logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {wait_time}s...")
                            time.sleep(wait_time)
                        else:
                            logger.error(f"All {max_attempts} attempts failed. Last error: {e}")
                
                raise last_exception
            return wrapper
        return decorator

class QueueManager:
    """队列管理器 - 支持独立并行处理"""

    def __init__(self, config: ServiceConfig):
        self.config = config
        self.queues = {
            'audio': queue.Queue(maxsize=config.queue_size),
            'image': queue.Queue(maxsize=config.queue_size),
            'video': queue.Queue(maxsize=config.queue_size),
            'stream': queue.Queue(maxsize=config.queue_size)
        }
        self.workers = {}
        self.results_store = {}
        self.worker_locks = {
            'audio': threading.Lock(),
            'image': threading.Lock(),
            'video': threading.Lock(),
            'stream': threading.Lock()
        }

    def submit_job(self, queue_name: str, job_data: Tuple) -> str:
        """提交任务到队列"""
        job_id = str(uuid.uuid4())

        try:
            self.queues[queue_name].put_nowait((job_id, *job_data))
            self.results_store[job_id] = JobResult(job_id=job_id)
            logger.info(f"Job {job_id} submitted to {queue_name} queue (queue size: {self.queues[queue_name].qsize()})")
            return job_id
        except queue.Full:
            raise Exception(f"Queue {queue_name} is full")

    def get_job_status(self, job_id: str) -> Optional[JobResult]:
        """获取任务状态"""
        return self.results_store.get(job_id)

    def cancel_job(self, job_id: str) -> bool:
        """取消任务"""
        if job_id in self.results_store:
            self.results_store[job_id].cancelled = True
            return True
        return False

    def get_queue_stats(self) -> Dict[str, Dict[str, int]]:
        """获取队列统计信息"""
        stats = {}
        for queue_name, q in self.queues.items():
            stats[queue_name] = {
                'size': q.qsize(),
                'maxsize': q.maxsize,
                'processing_jobs': len([
                    job for job in self.results_store.values()
                    if job.status == 'processing' and queue_name in str(job.job_id)
                ])
            }
        return stats

# 全局实例
config = ServiceConfig()
monitor = PerformanceMonitor()
resource_manager = ResourceManager(config)
queue_manager = QueueManager(config)

# Flask应用初始化
app = Flask(__name__)
CORS(app)

# 加载配置
def load_config(config_file='config_zhoushan_optimized.yaml'):
    """加载配置文件"""
    # 尝试加载优化配置，如果不存在则使用原配置
    config_files = [config_file, 'config_zhoushan.yaml']

    for config_path in config_files:
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    logger.info(f"Loading config from: {config_path}")
                    return yaml.safe_load(f)
        except Exception as e:
            logger.warning(f"Failed to load config from {config_path}: {e}")
            continue

    raise FileNotFoundError("No valid configuration file found")

cfg = load_config()
logger.info("Configuration loaded successfully")

# 工具函数
def estimate_remaining_time(start_time: float, current_time: float, progress: float) -> str:
    """估算剩余时间"""
    if progress <= 0:
        return 'Calculating...'
    
    elapsed_time = current_time - start_time
    estimated_total_time = elapsed_time / progress
    remaining_time = estimated_total_time - elapsed_time
    
    if remaining_time < 60:
        return f'{round(remaining_time, 1)}秒'
    elif remaining_time < 3600:
        return f'{round(remaining_time / 60, 1)}分钟'
    else:
        return f'{round(remaining_time / 3600, 1)}小时'

@lru_cache(maxsize=128)
def get_color_palette(classes_tuple: Tuple[str, ...]) -> List[Tuple[int, int, int]]:
    """生成颜色调色板（带缓存）"""
    import colorsys
    palette = []
    
    for i, cls in enumerate(classes_tuple):
        hue = (i * 0.618033988749895) % 1  # 黄金比例
        saturation = 0.8
        value = 0.9
        
        r, g, b = colorsys.hsv_to_rgb(hue, saturation, value)
        palette.append((int(r * 255), int(g * 255), int(b * 255)))
    
    return palette

def validate_file_type(filename: str, allowed_extensions: List[str]) -> bool:
    """验证文件类型"""
    if not filename or '.' not in filename:
        return False
    
    extension = filename.rsplit('.', 1)[1].lower()
    return extension in allowed_extensions

# API路由
@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    metrics = monitor.get_system_metrics()
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'metrics': metrics
    })

@app.route('/metrics', methods=['GET'])
def get_metrics():
    """获取性能指标 - 增强版本，支持独立并行处理监控"""
    return jsonify({
        'system': monitor.get_system_metrics(),
        'queues': queue_manager.get_queue_stats(),
        'workers': worker_manager.get_worker_stats(),
        'models': model_manager.get_model_stats(),
        'active_jobs': len([
            job for job in queue_manager.results_store.values()
            if job.status == 'processing'
        ]),
        'total_jobs': len(queue_manager.results_store),
        'completed_jobs': len([
            job for job in queue_manager.results_store.values()
            if job.status == 'completed'
        ]),
        'failed_jobs': len([
            job for job in queue_manager.results_store.values()
            if job.status == 'failed'
        ]),
        'parallel_processing': {
            'audio_workers': config.audio_workers,
            'image_workers': config.image_workers,
            'video_workers': config.video_workers,
            'stream_workers': config.stream_workers
        }
    })

@app.route('/check_status/<job_id>', methods=['GET'])
def check_job_status(job_id: str):
    """检查任务状态 - 修改为符合要求的格式"""
    job = queue_manager.get_job_status(job_id)

    if not job:
        return jsonify({'error': 'Invalid job ID'}), 404, {'Content-Type': 'application/json'}

    # 根据任务类型返回不同格式
    # 对于图像检测任务，返回简化格式
    if hasattr(job, 'result') and job.result and isinstance(job.result, list):
        # 单图识别格式
        result_data = {
            'status': job.status,
            'start_time': job.start_time.strftime('%Y-%m-%d %H:%M:%S') if job.start_time else None,
            'end_time': job.end_time.strftime('%Y-%m-%d %H:%M:%S') if job.end_time else None,
            'file_id': getattr(job, 'file_id', ''),
            'result': job.result
        }

        # 如果是完成状态，添加处理时间
        if job.status == 'completed' and job.start_time and job.end_time:
            result_data['estimated_processing_time'] = (job.end_time - job.start_time).total_seconds()
    else:
        # 其他任务类型保持原格式（如音频识别）
        result_data = {
            'status': job.status,
            'cancelled': job.status == 'cancelled',
            'progress': job.progress or '0/0',
            'estimated_remaining_time': job.estimated_remaining_time or '',
            'start_time': job.start_time.strftime('%Y-%m-%d %H:%M:%S') if job.start_time else None,
            'end_time': job.end_time.strftime('%Y-%m-%d %H:%M:%S') if job.end_time else None,
            'file_id': getattr(job, 'file_id', ''),
            'result': job.result,
            'error': job.error
        }

        # 如果是完成状态，添加处理时间（兼容旧版本字段名）
        if job.status == 'completed' and job.start_time and job.end_time:
            result_data['estimated_processing_time'] = (job.end_time - job.start_time).total_seconds()

    return jsonify(result_data), 200, {'Content-Type': 'application/json'}

@app.route('/cancel_job/<job_id>', methods=['POST'])
def cancel_job(job_id: str):
    """取消任务 - 兼容旧版本格式"""
    job = queue_manager.get_job_status(job_id)
    if not job:
        return jsonify({'error': 'Invalid job ID', 'status': 'failed'}), 404, {'Content-Type': 'application/json'}

    if job.status == 'completed':
        return jsonify({'error': 'Cannot cancel a completed task', 'status': 'failed'}), 400, {'Content-Type': 'application/json'}

    if queue_manager.cancel_job(job_id):
        return jsonify({'job_id': job_id, 'status': 'cancelled'}), 200, {'Content-Type': 'application/json'}
    else:
        return jsonify({'error': 'Invalid job ID', 'status': 'failed'}), 404, {'Content-Type': 'application/json'}

@app.route('/parallel_status', methods=['GET'])
def get_parallel_status():
    """获取独立并行处理状态"""
    try:
        return jsonify({
            'parallel_processing_enabled': True,
            'worker_configuration': {
                'audio_workers': config.audio_workers,
                'image_workers': config.image_workers,
                'video_workers': config.video_workers,
                'stream_workers': config.stream_workers
            },
            'current_status': {
                'queues': queue_manager.get_queue_stats(),
                'workers': worker_manager.get_worker_stats(),
                'models': model_manager.get_model_stats()
            },
            'processing_independence': {
                'audio_processing': 'Independent - Multiple workers with thread-safe model access',
                'image_processing': 'Independent - Multiple workers with parallel YOLO inference',
                'video_processing': 'Independent - Multiple workers with parallel processing',
                'stream_processing': 'Independent - Separate threads for each camera stream'
            },
            'performance_benefits': [
                'Audio, image, and video processing run in completely separate worker threads',
                'Multiple workers per processing type prevent blocking',
                'Thread-safe model access allows true parallel inference',
                'Stream processing runs independently from other tasks',
                'No single point of failure between processing types'
            ]
        }), 200
    except Exception as e:
        logger.error(f"Get parallel status error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/gpu_optimization_status', methods=['GET'])
def get_gpu_optimization_status():
    """获取GPU硬件解码优化状态"""
    try:
        gpu_config = cfg.get('model', {}).get('vision', {}).get('stream', {}).get('gpu_optimization', {})

        # 检查GPU可用性
        gpu_available = torch.cuda.is_available()
        gpu_count = torch.cuda.device_count() if gpu_available else 0
        current_device = torch.cuda.current_device() if gpu_available else None
        gpu_name = torch.cuda.get_device_name(current_device) if gpu_available and current_device is not None else None

        # 检查OpenCV FFmpeg支持
        opencv_info = cv2.getBuildInformation()
        ffmpeg_support = 'FFMPEG' in opencv_info

        return jsonify({
            'gpu_optimization_enabled': gpu_config.get('enabled', True),
            'gpu_hardware_status': {
                'cuda_available': gpu_available,
                'gpu_count': gpu_count,
                'current_device': current_device,
                'gpu_name': gpu_name,
                'gpu_memory_allocated': torch.cuda.memory_allocated() / 1024**3 if gpu_available else 0,  # GB
                'gpu_memory_reserved': torch.cuda.memory_reserved() / 1024**3 if gpu_available else 0     # GB
            },
            'opencv_support': {
                'ffmpeg_enabled': ffmpeg_support,
                'opencv_version': cv2.__version__
            },
            'gpu_configuration': {
                'device_id': gpu_config.get('device_id', 0),
                'decoder': gpu_config.get('decoder', 'h264_cuvid'),
                'encoder': gpu_config.get('encoder', 'h264_nvenc'),
                'buffer_size': gpu_config.get('buffer_size', 1024000),
                'max_delay': gpu_config.get('max_delay', 500000),
                'decode_threads': gpu_config.get('decode_threads', 4),
                'low_latency': gpu_config.get('low_latency', True),
                'tcp_transport': gpu_config.get('tcp_transport', True)
            },
            'optimization_benefits': [
                'GPU硬件解码显著减少CPU使用率',
                'NVIDIA CUVID解码器提供更高的解码性能',
                'GPU编码器(NVENC)加速视频录制',
                '低延迟配置减少流处理延迟',
                '智能回退机制确保兼容性'
            ],
            'performance_recommendations': [
                '确保使用支持硬件解码的GPU驱动程序',
                '监控GPU内存使用避免溢出',
                '根据流数量调整缓冲区大小',
                '使用TCP传输提高稳定性'
            ]
        }), 200
    except Exception as e:
        logger.error(f"Get GPU optimization status error: {e}")
        return jsonify({'error': str(e)}), 500

# 模型管理器
class ModelManager:
    """模型管理器 - 负责模型加载、缓存和推理，支持线程安全的并行访问"""

    def __init__(self, config_dict: Dict):
        self.config = config_dict
        self.models = {}
        self.device = config_dict['model']['device']
        self.model_locks = {}  # 为每个模型类型创建锁
        self.inference_stats = defaultdict(int)  # 推理统计
        self._load_models()

    def _load_models(self):
        """加载所有模型"""
        logger.info("Loading models...")

        try:
            # 加载视觉模型
            self._load_vision_models()

            # 加载音频模型
            self._load_audio_models()

            logger.info("All models loaded successfully")

        except Exception as e:
            logger.error(f"Failed to load models: {e}")
            raise

    def _load_vision_models(self):
        """加载视觉模型"""
        from cryptography.fernet import Fernet

        # 解密模型文件
        def decrypt_model(encrypted_path: str) -> str:
            key = 'JLTMtAiHi_hG6fTCuiRBLtH9A4j8cEuK1a4eeNtfqQM='
            fernet = Fernet(key)

            with open(encrypted_path, "rb") as encrypted_file:
                encrypted_data = encrypted_file.read()

            decrypted_data = fernet.decrypt(encrypted_data)

            temp_file = tempfile.NamedTemporaryFile(suffix=".pt", delete=False)
            temp_file.write(decrypted_data)
            temp_file.close()

            # 记录临时文件以便后续清理
            if not hasattr(self, '_temp_files'):
                self._temp_files = []
            self._temp_files.append(temp_file.name)

            return temp_file.name

        # 加载鸟类检测模型
        bird_model_path = decrypt_model(self.config['model']['vision']['yolov12_ckp_bird'] + ".enc")
        self.models['bird_detection'] = YOLO(bird_model_path)
        self.model_locks['bird_detection'] = threading.Lock()
        os.remove(bird_model_path)

        # 加载相机陷阱模型
        cameratrap_model_path = decrypt_model(self.config['model']['vision']['yolov12_ckp_cameratrap'] + ".enc")
        self.models['cameratrap_detection'] = YOLO(cameratrap_model_path)
        self.model_locks['cameratrap_detection'] = threading.Lock()
        os.remove(cameratrap_model_path)

        logger.info("Vision models loaded")

    def _load_audio_models(self):
        """加载音频模型 - 使用Beijing birdsound的实现"""
        try:
            # 导入Beijing birdsound的模型管理器
            from beijing_birdsound_model import BeijingBirdsoundModelManager

            # 创建Beijing birdsound模型管理器实例
            self.models['beijing_birdsound'] = BeijingBirdsoundModelManager(self.config)
            self.model_locks['beijing_birdsound'] = threading.Lock()

            logger.info("Beijing birdsound audio models loaded")
        except Exception as e:
            logger.error(f"Failed to load Beijing birdsound models: {e}")
            # 回退到原来的AST模型（如果Beijing birdsound模型加载失败）
            try:
                self.models['audio_classification'] = pipeline(
                    task="audio-classification",
                    model=self.config['model']['sound'].get('ast_ckp', 'filedata/AiModel/model/transformers/ast/checkpoint-17812'),
                    device=self.device
                )
                self.model_locks['audio_classification'] = threading.Lock()
                logger.warning("Fallback to AST model due to Beijing birdsound model loading failure")
            except Exception as fallback_error:
                logger.error(f"Failed to load fallback AST model: {fallback_error}")
                raise

    @RetryHandler.with_retry(max_attempts=3)
    def predict_image(self, image_data, model_type: str = 'bird_detection', **kwargs) -> Dict:
        """图像预测 - 支持线程安全的并行推理"""
        start_time = time.time()
        worker_name = threading.current_thread().name

        try:
            model_key = model_type
            if model_key not in self.models:
                raise ValueError(f"Unknown model type: {model_type}")

            model = self.models[model_key]

            # 记录推理统计
            self.inference_stats[f'{model_key}_requests'] += 1

            logger.debug(f"[{worker_name}] Starting image inference with {model_key}")

            # 执行推理 - YOLO模型是线程安全的，不需要锁
            results = model.predict(
                source=image_data,
                imgsz=kwargs.get('imgsz', 1536),
                conf=kwargs.get('conf', 0.65),
                device=self.device,
                save=False,
                show=False,
                verbose=False
            )

            # 处理结果
            result = results[0]
            bboxes = result.boxes.xyxy.cpu().numpy().tolist() if result.boxes is not None else []
            labels = result.boxes.cls.cpu().numpy().tolist() if result.boxes is not None else []
            scores = result.boxes.conf.cpu().numpy().tolist() if result.boxes is not None else []

            class_names = model.names
            classes = [class_names[int(label)] for label in labels]

            # 生成调色板
            palette = get_color_palette(tuple(classes)) if classes else []

            inference_time = time.time() - start_time
            monitor.record_metric('image_inference_time', inference_time)

            return {
                'bboxes': bboxes,
                'labels': [int(l) for l in labels],
                'scores': scores,
                'classes': classes,
                'palette': palette,
                'inference_time': inference_time
            }

        except Exception as e:
            logger.error(f"Image prediction failed: {e}")
            raise

    @RetryHandler.with_retry(max_attempts=3)
    def predict_audio(self, audio_path: str, **kwargs) -> Dict:
        """音频预测 - 使用Beijing birdsound的实现"""
        start_time = time.time()
        worker_name = threading.current_thread().name

        try:
            # 优先使用Beijing birdsound模型
            if 'beijing_birdsound' in self.models:
                model = self.models['beijing_birdsound']

                # 记录推理统计
                self.inference_stats['beijing_birdsound_requests'] += 1

                logger.debug(f"[{worker_name}] Starting Beijing birdsound audio inference")

                # 使用Beijing birdsound的predict_audio方法
                with self.model_locks['beijing_birdsound']:
                    results = model.predict_audio(audio_path, **kwargs)

                inference_time = time.time() - start_time
                monitor.record_metric('audio_inference_time', inference_time)

                return results

            else:
                # 回退到AST模型
                model = self.models['audio_classification']

                # 记录推理统计
                self.inference_stats['audio_requests'] += 1

                logger.debug(f"[{worker_name}] Starting AST audio inference (fallback)")

                # 使用锁确保音频模型的线程安全访问
                with self.model_locks['audio_classification']:
                    results = model(audio_path)

                # 过滤结果并转换为Beijing birdsound格式
                threshold = kwargs.get('threshold', self.config['model']['sound'].get('threshold', 0.65))
                filtered_results = [
                    item for item in results
                    if item['score'] > threshold
                ]

                inference_time = time.time() - start_time
                monitor.record_metric('audio_inference_time', inference_time)

                # 转换为Beijing birdsound格式
                return {
                    'results': [{
                        'segment_id': 0,
                        'start_time': 0,
                        'end_time': 10,  # 默认10秒
                        'classes': [item['label'] for item in filtered_results],
                        'scores': [item['score'] for item in filtered_results]
                    }] if filtered_results else [],
                    'inference_time': inference_time,
                    'time_window': 10,
                    'total_segments': 1
                }

        except Exception as e:
            logger.error(f"Audio prediction failed: {e}")
            raise

    def get_model_stats(self) -> Dict[str, Any]:
        """获取模型统计信息"""
        return {
            'loaded_models': list(self.models.keys()),
            'inference_stats': dict(self.inference_stats),
            'device': self.device,
            'model_locks': list(self.model_locks.keys())
        }

    def get_model_stats(self) -> Dict[str, Any]:
        """获取模型统计信息"""
        return {
            'loaded_models': list(self.models.keys()),
            'inference_stats': dict(self.inference_stats),
            'device': self.device,
            'model_locks': list(self.model_locks.keys())
        }

# 文件处理器
class FileProcessor:
    """文件处理器 - 负责文件上传、转换和管理"""

    def __init__(self, config_dict: Dict):
        self.config = config_dict
        self.temp_dir = config_dict['general']['tmp_path']
        self.minio_client = self._init_minio()

    def _init_minio(self) -> Minio:
        """初始化Minio客户端"""
        return Minio(
            self.config['minio']['url'],
            access_key=self.config['minio']['access_key'],
            secret_key=self.config['minio']['secret_key'],
            secure=self.config['minio']['secure']
        )

    @RetryHandler.with_retry(max_attempts=3)
    def save_base64_file(self, base64_data: str, file_extension: str = '') -> str:
        """保存Base64数据为临时文件"""
        try:
            # 解码数据
            file_data = base64.b64decode(base64_data)

            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(
                delete=False,
                suffix=file_extension,
                dir=self.temp_dir
            )
            temp_file.write(file_data)
            temp_file.close()

            return temp_file.name

        except Exception as e:
            logger.error(f"Failed to save base64 file: {e}")
            raise

    @RetryHandler.with_retry(max_attempts=3)
    def convert_audio_format(self, input_path: str, target_format: str = 'wav') -> str:
        """转换音频格式"""
        try:
            from pydub import AudioSegment
            import filetype

            # 检测文件类型
            kind = filetype.guess(input_path)
            if kind is None:
                raise ValueError("无法识别文件格式")

            # 加载音频
            if kind.mime == "audio/mpeg":
                audio = AudioSegment.from_mp3(input_path)
            elif kind.mime == "audio/x-wav":
                audio = AudioSegment.from_wav(input_path)
            else:
                raise ValueError(f"不支持的音频格式: {kind.mime}")

            # 转换为目标格式
            if target_format == 'wav':
                # 标准化音频参数
                audio = audio.set_channels(1)  # 单声道
                audio = audio.set_frame_rate(self.config['model']['sound']['sample_rate'])

                output_path = input_path + f".{target_format}"
                audio.export(output_path, format=target_format)

                return output_path
            else:
                raise ValueError(f"不支持的目标格式: {target_format}")

        except Exception as e:
            logger.error(f"Audio conversion failed: {e}")
            raise

    @RetryHandler.with_retry(max_attempts=3)
    def upload_to_minio(self, file_path: str, object_name: str, content_type: str, folder: str = '') -> str:
        """上传文件到Minio"""
        try:
            bucket_name = self.config['minio']['bucket']

            # 确保bucket存在
            if not self.minio_client.bucket_exists(bucket_name):
                self.minio_client.make_bucket(bucket_name)

            # 构建对象路径
            if folder:
                object_path = f"{folder}/{object_name}"
            else:
                object_path = object_name

            # 上传文件
            file_size = os.path.getsize(file_path)
            with open(file_path, "rb") as file_data:
                self.minio_client.put_object(
                    bucket_name,
                    object_path,
                    file_data,
                    file_size,
                    content_type=content_type
                )

            # 生成下载URL
            download_url = f"http://{self.config['minio']['public_url']}/{bucket_name}/{object_path}"

            # 清理本地文件
            if os.path.exists(file_path):
                os.remove(file_path)

            return download_url

        except Exception as e:
            logger.error(f"Minio upload failed: {e}")
            raise

# 业务处理器
class AudioProcessor:
    """音频处理器"""

    def __init__(self, model_manager: ModelManager, file_processor: FileProcessor, config_dict: Dict):
        self.model_manager = model_manager
        self.file_processor = file_processor
        self.config = config_dict

    def process_audio_job(self, job_id: str, file_path: str, file_id: str = None):
        """处理音频识别任务 - 使用Beijing birdsound的处理流程"""
        job = queue_manager.get_job_status(job_id)
        if not job:
            return

        try:
            job.status = 'processing'
            job.start_time = datetime.now()
            job.file_id = file_id

            # 转换音频格式
            wav_path = self.file_processor.convert_audio_format(file_path)

            # 生成频谱图Base64编码 - 使用Beijing birdsound的方法
            spectrogram_base64 = self._generate_spectrogram_info(wav_path)

            # 执行鸟声识别 - 直接使用完整音频，不分段
            inference_start_time = time.time()
            prediction = self.model_manager.predict_audio(wav_path)
            inference_duration = time.time() - inference_start_time

            # 处理结果 - 使用Beijing birdsound的格式
            job.status = 'completed'
            job.end_time = datetime.now()
            job.result = {
                'duration': inference_duration,
                'results': prediction.get('results', []),
                'spectrogram': spectrogram_base64,
                'time_window': prediction.get('time_window', 10),
                'total_segments': prediction.get('total_segments', 1)
            }

            # 发送回调
            self._send_callback(job)

            # 清理文件
            if os.path.exists(wav_path):
                os.remove(wav_path)
            if os.path.exists(file_path):
                os.remove(file_path)

        except Exception as e:
            logger.error(f"Audio processing failed for job {job_id}: {e}")
            job.status = 'failed'
            job.error = str(e)

    def _generate_spectrogram_info(self, audio_path: str) -> str:
        """生成频谱图Base64编码 - 使用Beijing birdsound的方法"""
        try:
            import matplotlib.pyplot as plt
            import librosa
            import librosa.display
            from io import BytesIO
            import numpy as np

            # 加载音频
            y, sr = librosa.load(audio_path, sr=self.config['model']['sound']['preprocessing']['sample_rate'], mono=True)

            # 限制音频长度
            max_duration = self.config['model']['sound']['spectrogram']['max_duration']
            if len(y) > max_duration * sr:
                y = y[:int(max_duration * sr)]

            # 生成Mel频谱图
            n_fft = self.config['model']['sound']['spectrogram']['n_fft']
            hop_length = self.config['model']['sound']['spectrogram']['hop_length']
            n_mels = self.config['model']['sound']['spectrogram']['n_mels']
            fmin = self.config['model']['sound']['spectrogram']['fmin']
            fmax = self.config['model']['sound']['spectrogram']['fmax']

            mel_spec = librosa.feature.melspectrogram(
                y=y, sr=sr, n_fft=n_fft, hop_length=hop_length,
                n_mels=n_mels, fmin=fmin, fmax=fmax
            )

            # 转换为dB
            mel_spec_db = librosa.power_to_db(mel_spec, ref=np.max)

            # 创建图像
            plt.figure(figsize=(
                self.config['model']['sound']['spectrogram']['fig_x'],
                self.config['model']['sound']['spectrogram']['fig_y']
            ))

            librosa.display.specshow(
                mel_spec_db, sr=sr, hop_length=hop_length,
                x_axis='time', y_axis='mel', fmin=fmin, fmax=fmax
            )

            plt.colorbar(format='%+2.0f dB')
            plt.title('Mel-frequency spectrogram')
            plt.tight_layout()

            # 保存为Base64
            img_buffer = BytesIO()
            plt.savefig(
                img_buffer,
                format=self.config['model']['sound']['spectrogram']['format'],
                bbox_inches='tight',
                dpi=100
            )
            plt.close()

            img_buffer.seek(0)
            return base64.b64encode(img_buffer.read()).decode('utf-8')

        except Exception as e:
            logger.warning(f"Failed to generate spectrogram: {e}")
            return ""



    def _send_callback(self, job: JobResult):
        """发送回调"""
        try:
            callback_url = self.config['model']['sound']['callback_url']
            callback_data = {
                'job_id': job.job_id,
                'status': job.status,
                'start_time': job.start_time.isoformat() if job.start_time else None,
                'end_time': job.end_time.isoformat() if job.end_time else None,
                'result': job.result
            }

            response = requests.post(
                callback_url,
                json=callback_data,
                timeout=self.config['model']['sound']['callback_timeout']
            )

            logger.info(f"Callback sent for job {job.job_id}: {response.status_code}")

        except Exception as e:
            logger.error(f"Callback failed for job {job.job_id}: {e}")

class ImageProcessor:
    """图像处理器"""

    def __init__(self, model_manager: ModelManager, file_processor: FileProcessor, config_dict: Dict):
        self.model_manager = model_manager
        self.file_processor = file_processor
        self.config = config_dict

    def process_image_job(self, job_id: str, file_path: str, file_id: str = None, model_type: str = 'bird_detection'):
        """处理图像检测任务"""
        job = queue_manager.get_job_status(job_id)
        if not job:
            return

        try:
            job.status = 'processing'
            job.start_time = datetime.now()

            # 执行推理
            prediction = self.model_manager.predict_image(
                file_path,
                model_type=model_type,
                imgsz=self.config['model']['vision']['img']['size'],
                conf=self.config['model']['vision']['img']['score_thr']
            )

            # 绘制检测结果（可选，根据需要保留或移除）
            # output_image_path = self._draw_detections(file_path, prediction)

            # 上传结果图像（已移除，按要求不返回图像URL）
            # image_url = self.file_processor.upload_to_minio(
            #     output_image_path,
            #     f"{job_id}_result.jpg",
            #     "image/jpeg",
            #     "detection_results"
            # )

            # 完成处理 - 修改为符合要求的格式
            job.status = 'completed'
            job.end_time = datetime.now()
            job.file_id = file_id if file_id else ''

            # 按照要求的格式构建result数组
            job.result = [{
                'bboxes': prediction['bboxes'],
                'classes': prediction['classes'],
                'labels': prediction['labels'],
                'palette': prediction['palette'],
                'scores': prediction['scores']
            }] if prediction['bboxes'] else []

            # 发送回调
            self._send_callback(job)

            # 清理文件
            if os.path.exists(file_path):
                os.remove(file_path)

        except Exception as e:
            logger.error(f"Image processing failed for job {job_id}: {e}")
            job.status = 'failed'
            job.error = str(e)

    def _draw_detections(self, image_path: str, prediction: Dict) -> str:
        """绘制检测结果"""
        try:
            image = cv2.imread(image_path)

            for i, (bbox, cls, score) in enumerate(zip(
                prediction['bboxes'],
                prediction['classes'],
                prediction['scores']
            )):
                if i < len(prediction['palette']):
                    color = prediction['palette'][i]
                else:
                    color = (0, 255, 0)  # 默认绿色

                # 绘制边界框
                x1, y1, x2, y2 = map(int, bbox)
                cv2.rectangle(image, (x1, y1), (x2, y2), color, 2)

                # 绘制标签
                label = f"{cls}: {score:.2f}"
                cv2.putText(image, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

            # 保存结果图像
            output_path = image_path.replace('.', '_result.')
            cv2.imwrite(output_path, image)

            return output_path

        except Exception as e:
            logger.error(f"Failed to draw detections: {e}")
            return image_path

    def _send_callback(self, job: JobResult):
        """发送回调"""
        try:
            callback_url = self.config['model']['vision']['img']['callback_url']
            callback_data = {
                'job_id': job.job_id,
                'status': job.status,
                'start_time': job.start_time.isoformat() if job.start_time else None,
                'end_time': job.end_time.isoformat() if job.end_time else None,
                'result': job.result
            }

            response = requests.post(
                callback_url,
                json=callback_data,
                timeout=self.config['model']['vision']['img']['callback_timeout']
            )

            logger.info(f"Callback sent for job {job.job_id}: {response.status_code}")

        except Exception as e:
            logger.error(f"Callback failed for job {job.job_id}: {e}")

class VideoProcessor:
    """视频处理器 - 处理录制的视频文件"""

    def __init__(self, model_manager: ModelManager, file_processor: FileProcessor, config_dict: Dict):
        self.model_manager = model_manager
        self.file_processor = file_processor
        self.config = config_dict

    def process_video_job(self, job_id: str, video_path: str, stream_url: str, stream_key: str):
        """处理视频检测任务"""
        job = queue_manager.get_job_status(job_id)
        if not job:
            return

        try:
            job.status = 'processing'
            job.start_time = datetime.now()

            logger.info(f"Processing video: {video_path}")

            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise Exception(f"Cannot open video file: {video_path}")

            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # 创建输出视频
            output_path = video_path.replace('.mp4', '_processed.mp4')
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            # 处理结果
            results = []
            classes_detected = {}
            frame_count = 0
            start_time = time.time()

            while True:
                if job.cancelled:
                    job.status = 'cancelled'
                    break

                ret, frame = cap.read()
                if not ret:
                    break

                # 执行推理
                detection_result = self.model_manager.predict_image(
                    frame,
                    model_type='bird_detection',
                    imgsz=self.config.get('model', {}).get('vision', {}).get('stream', {}).get('size', 672),
                    conf=self.config.get('model', {}).get('vision', {}).get('stream', {}).get('score_thr', 0.65)
                )

                # 绘制检测结果
                processed_frame = self._draw_detections_on_frame(frame, detection_result)
                out.write(processed_frame)

                # 记录检测结果
                if detection_result and detection_result.get('classes'):
                    for cls in detection_result['classes']:
                        classes_detected[cls] = classes_detected.get(cls, 0) + 1

                    results.append({
                        'frame': frame_count,
                        'timestamp': frame_count / fps,
                        'detections': detection_result
                    })

                frame_count += 1

                # 更新进度
                progress = frame_count / total_frames
                job.progress = f'{frame_count}/{total_frames}'
                job.estimated_remaining_time = estimate_remaining_time(start_time, time.time(), progress)

            # 释放资源
            cap.release()
            out.release()

            # 检查是否有足够的检测结果
            valid_detect_count = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('valid_detect_count', 15)
            total_detections = sum(classes_detected.values())

            if total_detections >= valid_detect_count:
                # 提取第一帧作为缩略图
                thumbnail_path = self._extract_first_frame(video_path)

                # 上传文件到Minio
                stream_device = self._get_stream_device_from_key(stream_key)

                # 上传处理后的视频
                video_url = self.file_processor.upload_to_minio(
                    output_path,
                    f"{job_id}_processed.mp4",
                    "video/mp4",
                    stream_device
                )

                # 上传缩略图
                thumbnail_url = None
                if thumbnail_path:
                    thumbnail_url = self.file_processor.upload_to_minio(
                        thumbnail_path,
                        f"{job_id}_thumbnail.jpg",
                        "image/jpeg",
                        stream_device
                    )

                # 完成处理
                job.status = 'completed'
                job.end_time = datetime.now()
                job.result = {
                    'video_url': video_url,
                    'thumbnail_url': thumbnail_url,
                    'total_detections': total_detections,
                    'classes_detected': classes_detected,
                    'total_frames': total_frames,
                    'duration': total_frames / fps,
                    'stream_device': stream_device
                }

                # 发送回调
                self._send_callback(job)

                logger.info(f"Video processing completed: {job_id}, detections: {total_detections}")

            else:
                # 检测结果不足，删除文件
                if os.path.exists(output_path):
                    os.remove(output_path)

                job.status = 'completed'
                job.end_time = datetime.now()
                job.result = {
                    'message': 'Insufficient detections',
                    'total_detections': total_detections,
                    'required_detections': valid_detect_count
                }

                logger.info(f"Video processing completed but insufficient detections: {job_id}")

            # 清理原始文件
            if os.path.exists(video_path):
                os.remove(video_path)

        except Exception as e:
            logger.error(f"Video processing failed for job {job_id}: {e}")
            job.status = 'failed'
            job.error = str(e)

            # 清理文件
            for path in [video_path, output_path]:
                if os.path.exists(path):
                    os.remove(path)

    def _draw_detections_on_frame(self, frame, detection_result):
        """在帧上绘制检测结果"""
        if not detection_result or not detection_result.get('bboxes'):
            return frame

        processed_frame = frame.copy()

        for i, (bbox, cls, score) in enumerate(zip(
            detection_result['bboxes'],
            detection_result['classes'],
            detection_result['scores']
        )):
            # 获取颜色
            if i < len(detection_result.get('palette', [])):
                color = detection_result['palette'][i]
            else:
                color = (0, 255, 0)  # 默认绿色

            # 绘制边界框
            x1, y1, x2, y2 = map(int, bbox)
            cv2.rectangle(processed_frame, (x1, y1), (x2, y2), color, 2)

            # 绘制标签
            label = f"{cls}: {score:.2f}"
            cv2.putText(processed_frame, label, (x1, y1 - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

        return processed_frame

    def _extract_first_frame(self, video_path: str) -> Optional[str]:
        """提取视频第一帧"""
        try:
            cap = cv2.VideoCapture(video_path)
            ret, frame = cap.read()
            cap.release()

            if ret:
                thumbnail_path = video_path.replace('.mp4', '_thumbnail.jpg')
                cv2.imwrite(thumbnail_path, frame)
                return thumbnail_path
            else:
                return None

        except Exception as e:
            logger.error(f"Failed to extract first frame: {e}")
            return None

    def _get_stream_device_from_key(self, stream_key: str) -> str:
        """从流键中提取设备ID"""
        # stream_key格式: stream_url_device_id_count
        parts = stream_key.split('_')
        if len(parts) >= 2:
            return parts[-2]  # 设备ID通常在倒数第二个位置
        return 'unknown'

    def _send_callback(self, job: JobResult):
        """发送回调"""
        try:
            callback_url = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('callback_url')
            if not callback_url:
                return

            callback_data = {
                'job_id': job.job_id,
                'status': job.status,
                'minio_url': job.result.get('video_url'),
                'minio_img_url': job.result.get('thumbnail_url'),
                'result': {
                    'total_detections': job.result.get('total_detections', 0),
                    'classes_detected': job.result.get('classes_detected', {}),
                    'stream_device': job.result.get('stream_device'),
                    'duration': job.result.get('duration', 0)
                }
            }

            response = requests.post(
                callback_url,
                json=callback_data,
                timeout=self.config.get('model', {}).get('vision', {}).get('stream', {}).get('callback_timeout', 10)
            )

            logger.info(f"Callback sent for job {job.job_id}: {response.status_code}")

        except Exception as e:
            logger.error(f"Callback failed for job {job.job_id}: {e}")

# 工作线程管理
class WorkerManager:
    """工作线程管理器 - 支持独立并行处理"""

    def __init__(self, queue_manager: QueueManager, audio_processor: AudioProcessor,
                 image_processor: ImageProcessor, video_processor: VideoProcessor, config: ServiceConfig):
        self.queue_manager = queue_manager
        self.audio_processor = audio_processor
        self.image_processor = image_processor
        self.video_processor = video_processor
        self.config = config
        self.workers = {}
        self.running = True

    def start_workers(self):
        """启动所有工作线程 - 每种类型启动多个独立线程"""

        # 启动多个音频处理工作线程
        self.workers['audio'] = []
        for i in range(self.config.audio_workers):
            worker = threading.Thread(
                target=self._audio_worker,
                daemon=True,
                name=f'AudioWorker-{i}'
            )
            worker.start()
            self.workers['audio'].append(worker)

        # 启动多个图像处理工作线程
        self.workers['image'] = []
        for i in range(self.config.image_workers):
            worker = threading.Thread(
                target=self._image_worker,
                daemon=True,
                name=f'ImageWorker-{i}'
            )
            worker.start()
            self.workers['image'].append(worker)

        # 启动多个视频处理工作线程
        self.workers['video'] = []
        for i in range(self.config.video_workers):
            worker = threading.Thread(
                target=self._video_worker,
                daemon=True,
                name=f'VideoWorker-{i}'
            )
            worker.start()
            self.workers['video'].append(worker)

        logger.info(f"All worker threads started: Audio({self.config.audio_workers}), "
                   f"Image({self.config.image_workers}), Video({self.config.video_workers})")

    def _audio_worker(self):
        """音频处理工作线程 - 独立并行处理"""
        worker_name = threading.current_thread().name
        logger.info(f"Audio worker {worker_name} started")

        while self.running:
            try:
                job_data = self.queue_manager.queues['audio'].get(timeout=1.0)
                job_id, file_path, file_id = job_data

                logger.info(f"[{worker_name}] Processing audio job: {job_id}")

                # 使用线程锁确保独立处理
                with self.queue_manager.worker_locks['audio']:
                    start_time = time.time()
                    self.audio_processor.process_audio_job(job_id, file_path, file_id)
                    processing_time = time.time() - start_time
                    logger.info(f"[{worker_name}] Audio job {job_id} completed in {processing_time:.2f}s")

                self.queue_manager.queues['audio'].task_done()

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"[{worker_name}] Audio worker error: {e}")

    def _image_worker(self):
        """图像处理工作线程 - 独立并行处理"""
        worker_name = threading.current_thread().name
        logger.info(f"Image worker {worker_name} started")

        while self.running:
            try:
                job_data = self.queue_manager.queues['image'].get(timeout=1.0)
                job_id, file_path, file_id, model_type = job_data

                logger.info(f"[{worker_name}] Processing image job: {job_id}")

                # 独立处理，不使用锁以允许真正的并行
                start_time = time.time()
                self.image_processor.process_image_job(job_id, file_path, file_id, model_type)
                processing_time = time.time() - start_time
                logger.info(f"[{worker_name}] Image job {job_id} completed in {processing_time:.2f}s")

                self.queue_manager.queues['image'].task_done()

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"[{worker_name}] Image worker error: {e}")

    def _video_worker(self):
        """视频处理工作线程 - 独立并行处理"""
        worker_name = threading.current_thread().name
        logger.info(f"Video worker {worker_name} started")

        while self.running:
            try:
                job_data = self.queue_manager.queues['video'].get(timeout=1.0)
                job_id, video_path, stream_url, stream_key = job_data

                logger.info(f"[{worker_name}] Processing video job: {job_id}")

                # 独立处理，不使用锁以允许真正的并行
                start_time = time.time()
                self.video_processor.process_video_job(job_id, video_path, stream_url, stream_key)
                processing_time = time.time() - start_time
                logger.info(f"[{worker_name}] Video job {job_id} completed in {processing_time:.2f}s")

                self.queue_manager.queues['video'].task_done()

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"[{worker_name}] Video worker error: {e}")

    def stop_workers(self):
        """停止所有工作线程"""
        logger.info("Stopping all worker threads...")
        self.running = False

        # 停止所有类型的工作线程
        for worker_type, worker_list in self.workers.items():
            if isinstance(worker_list, list):
                for i, worker in enumerate(worker_list):
                    if worker.is_alive():
                        worker.join(timeout=5.0)
                        logger.info(f"Worker {worker_type}-{i} stopped")
            else:
                # 兼容旧版本单线程模式
                if worker_list.is_alive():
                    worker_list.join(timeout=5.0)
                    logger.info(f"Worker {worker_type} stopped")

        logger.info("All worker threads stopped")

    def get_worker_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取工作线程统计信息"""
        stats = {}
        for worker_type, worker_list in self.workers.items():
            if isinstance(worker_list, list):
                stats[worker_type] = {
                    'count': len(worker_list),
                    'alive': sum(1 for w in worker_list if w.is_alive()),
                    'names': [w.name for w in worker_list if w.is_alive()]
                }
            else:
                # 兼容旧版本单线程模式
                stats[worker_type] = {
                    'count': 1,
                    'alive': 1 if worker_list.is_alive() else 0,
                    'names': [worker_list.name] if worker_list.is_alive() else []
                }
        return stats

# 初始化核心组件
model_manager = ModelManager(cfg)
file_processor = FileProcessor(cfg)
audio_processor = AudioProcessor(model_manager, file_processor, cfg)
image_processor = ImageProcessor(model_manager, file_processor, cfg)

# 摄像头流处理器
class StreamProcessor:
    """优化的摄像头流处理器 - 集成轮转调度"""

    def __init__(self, model_manager: ModelManager, file_processor: FileProcessor, config_dict: Dict):
        self.model_manager = model_manager
        self.file_processor = file_processor
        self.config = config_dict
        self.streams_status = {}
        self.stream_threads = {}
        self.stop_event = threading.Event()
        self.paused_streams = {}
        self.recording_buffers = defaultdict(deque)

        # 初始化轮转调度器
        self.rotation_scheduler = None
        self._init_rotation_scheduler()

    def _init_rotation_scheduler(self):
        """初始化轮转调度器"""
        rotation_config = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('rotation', {})

        if not rotation_config.get('enabled', False):
            logger.info("Camera rotation scheduling disabled")
            return

        # 创建轮转配置
        config = RotationConfig(
            max_concurrent_streams=rotation_config.get('max_concurrent_streams', 2),
            rotation_interval=rotation_config.get('rotation_interval', 300),
            detection_extend_time=rotation_config.get('detection_extend_time', 60),
            min_monitoring_time=rotation_config.get('min_monitoring_time', 120),
            max_monitoring_time=rotation_config.get('max_monitoring_time', 600),
            priority_decay_factor=rotation_config.get('priority_decay_factor', 0.95),
            fairness_weight=rotation_config.get('fairness_weight', 0.7),
            detection_weight=rotation_config.get('detection_weight', 0.3)
        )

        # 获取摄像头配置
        cameras_config = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('cameras', {})

        # 创建调度器
        self.rotation_scheduler = CameraRotationScheduler(config, cameras_config)

        # 注册回调函数
        self.rotation_scheduler.register_rotation_callback('camera_activated', self._on_camera_activated)
        self.rotation_scheduler.register_rotation_callback('camera_rotated', self._on_camera_rotated)

        logger.info("Camera rotation scheduler initialized")

    # 移除复杂的URL构建函数，回归简单OpenCV实现

    # 移除复杂的GPU优化捕获器，回归简单OpenCV实现

    # 移除复杂的GPU优化写入器，回归简单OpenCV实现

    def _safe_frame_read(self, cap: cv2.VideoCapture, camera_name: str, max_retries: int = 3) -> Tuple[bool, Optional[np.ndarray]]:
        """安全的帧读取 - 带错误检测和恢复"""
        for attempt in range(max_retries):
            try:
                ret, frame = cap.read()

                if not ret:
                    logger.debug(f"Frame read failed for {camera_name}, attempt {attempt + 1}")
                    if attempt < max_retries - 1:
                        time.sleep(0.1)  # 短暂等待后重试
                        continue
                    return False, None

                # 检查帧是否有效
                if frame is None:
                    logger.debug(f"Received null frame for {camera_name}, attempt {attempt + 1}")
                    if attempt < max_retries - 1:
                        time.sleep(0.1)
                        continue
                    return False, None

                # 检查帧尺寸是否合理
                if frame.shape[0] < 10 or frame.shape[1] < 10:
                    logger.debug(f"Received invalid frame size for {camera_name}: {frame.shape}")
                    if attempt < max_retries - 1:
                        time.sleep(0.1)
                        continue
                    return False, None

                # 检查帧是否全黑（可能的解码错误）
                if np.mean(frame) < 1.0:  # 几乎全黑
                    logger.debug(f"Received potentially corrupted frame for {camera_name}")
                    if attempt < max_retries - 1:
                        time.sleep(0.1)
                        continue
                    # 即使是黑帧也返回，因为可能是正常的夜间场景

                return True, frame

            except Exception as e:
                logger.debug(f"Exception during frame read for {camera_name}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(0.1)
                    continue
                return False, None

        return False, None

    def _handle_stream_error(self, cap: cv2.VideoCapture, stream_url: str, camera_name: str, error_count: int) -> Tuple[cv2.VideoCapture, int]:
        """处理流错误 - 智能重连和错误恢复"""
        try:
            # 释放当前连接
            if cap is not None:
                cap.release()

            # 根据错误次数调整重连策略
            if error_count < 3:
                # 快速重连
                time.sleep(1)
                logger.info(f"Quick reconnect attempt for {camera_name}")
            elif error_count < 6:
                # 中等延迟重连
                time.sleep(5)
                logger.info(f"Medium delay reconnect for {camera_name}")
            else:
                # 长延迟重连
                time.sleep(30)
                logger.info(f"Long delay reconnect for {camera_name}")

            # 重新创建连接 - 使用简单的OpenCV
            new_cap = cv2.VideoCapture(stream_url + '?transportmode=unicast', cv2.CAP_FFMPEG)

            if new_cap.isOpened():
                logger.info(f"Successfully reconnected to {camera_name}")
                return new_cap, 0  # 重置错误计数
            else:
                logger.warning(f"Failed to reconnect to {camera_name}")
                return new_cap, error_count + 1

        except Exception as e:
            logger.error(f"Error during stream reconnection for {camera_name}: {e}")
            return None, error_count + 1

    def _optimize_memory_usage(self):
        """优化内存使用 - 清理和垃圾回收"""
        try:
            # 获取当前内存使用情况
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024

            if memory_mb > 8000:  # 超过8GB时进行激进清理
                logger.warning(f"High memory usage detected: {memory_mb:.1f}MB, performing aggressive cleanup")

                # 清理帧缓冲区
                for stream_key in list(self.streams_status.keys()):
                    if hasattr(self, 'frame_buffers') and stream_key in self.frame_buffers:
                        buffer = self.frame_buffers[stream_key]
                        if len(buffer) > 5:  # 只保留最近5帧
                            while len(buffer) > 5:
                                buffer.popleft()

                # 清理旧的任务结果
                current_time = time.time()
                old_jobs = []
                for job_id, job in queue_manager.results_store.items():
                    if job.end_time and (current_time - job.end_time.timestamp()) > 3600:  # 1小时前的任务
                        old_jobs.append(job_id)

                for job_id in old_jobs:
                    del queue_manager.results_store[job_id]

                logger.info(f"Cleaned {len(old_jobs)} old jobs")

                # 强制垃圾回收
                gc.collect()

                # 如果有CUDA，清理GPU内存
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                # 重新检查内存
                new_memory_mb = process.memory_info().rss / 1024 / 1024
                logger.info(f"Memory usage after cleanup: {new_memory_mb:.1f}MB (saved: {memory_mb - new_memory_mb:.1f}MB)")

            elif memory_mb > 6000:  # 超过6GB时进行温和清理
                logger.info(f"Moderate memory usage: {memory_mb:.1f}MB, performing light cleanup")

                # 清理部分帧缓冲区
                for stream_key in list(self.streams_status.keys()):
                    if hasattr(self, 'frame_buffers') and stream_key in self.frame_buffers:
                        buffer = self.frame_buffers[stream_key]
                        if len(buffer) > 10:  # 只保留最近10帧
                            while len(buffer) > 10:
                                buffer.popleft()

                # 轻量级垃圾回收
                gc.collect()

        except Exception as e:
            logger.error(f"Error during memory optimization: {e}")

    def _cleanup_corrupted_streams(self):
        """清理损坏的流连接"""
        try:
            corrupted_streams = []

            for stream_key, status in self.streams_status.items():
                if status.get('error_count', 0) > 10:  # 错误次数过多
                    corrupted_streams.append(stream_key)
                elif status.get('last_frame_time', 0) < time.time() - 300:  # 5分钟没有新帧
                    corrupted_streams.append(stream_key)

            for stream_key in corrupted_streams:
                logger.warning(f"Cleaning up corrupted stream: {stream_key}")
                self._stop_single_stream(stream_key)

                # 等待一段时间后重新启动
                def restart_stream():
                    time.sleep(60)  # 等待1分钟
                    if stream_key in self.streams_status:
                        camera_info = self._get_camera_info_by_stream_key(stream_key)
                        if camera_info:
                            self._start_single_stream(camera_info['url'], camera_info['id'], camera_info['name'])

                threading.Thread(target=restart_stream, daemon=True).start()

        except Exception as e:
            logger.error(f"Error during corrupted stream cleanup: {e}")

    def _on_camera_activated(self, camera_id: str, camera_stats):
        """摄像头激活回调"""
        logger.info(f"Camera activated by scheduler: {camera_id} ({camera_stats.camera_name})")

        # 启动摄像头流处理
        stream_url = camera_stats.stream_url
        camera_name = camera_stats.camera_name

        # 创建流处理线程
        thread = threading.Thread(
            target=self._process_scheduled_stream,
            args=(stream_url, camera_id, camera_name),
            daemon=True,
            name=f"ScheduledStream_{camera_id}"
        )

        stream_key = f"scheduled_{camera_id}"
        self.stream_threads[stream_key] = thread
        thread.start()

    def _on_camera_rotated(self, camera_id: str, camera_stats, reason: str):
        """摄像头轮转回调"""
        logger.info(f"Camera rotated by scheduler: {camera_id} ({camera_stats.camera_name}), reason: {reason}")

        # 停止对应的流处理线程
        stream_key = f"scheduled_{camera_id}"
        if stream_key in self.stream_threads:
            # 设置停止标志（线程会自行检查并退出）
            if stream_key in self.streams_status:
                self.streams_status[stream_key]['should_stop'] = True

    def start_all_streams(self):
        """启动所有摄像头流监控"""
        logger.info("Starting camera stream monitoring...")

        cameras = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('cameras', {})
        if not cameras:
            logger.warning("No cameras configured")
            return

        # 检查是否启用轮转调度
        if self.rotation_scheduler:
            logger.info("Using rotation scheduler for camera management")
            self.rotation_scheduler.start_scheduler()
            return

        # 传统模式：直接启动所有摄像头
        logger.info("Using traditional mode for camera management")
        count = 0
        for stream_url, camera_info in cameras.items():
            if camera_info.get('status') == '无安装':
                logger.info(f"Skipping uninstalled camera: {camera_info.get('name', 'Unknown')}")
                continue

            count += 1
            stream_id = camera_info.get('id', f'cam_{count}')
            camera_name = camera_info.get('name', f'Camera_{count}')

            # 检查是否超过最大并发流数量
            max_concurrent = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('processing', {}).get('max_concurrent_streams', 10)
            is_pre_paused = count > max_concurrent

            if is_pre_paused:
                logger.info(f"Pre-pausing stream {camera_name} (exceeds max concurrent limit)")

            # 创建流处理线程
            thread = threading.Thread(
                target=self._process_single_stream,
                args=(stream_url, stream_id, camera_name, count, is_pre_paused),
                daemon=True,
                name=f"Stream_{stream_id}"
            )

            stream_key = f"{stream_url}_{stream_id}_{count}"
            self.stream_threads[stream_key] = thread
            thread.start()

            # 避免同时启动太多流
            time.sleep(0.5)

        logger.info(f"Started monitoring {count} camera streams")

    def _process_scheduled_stream(self, stream_url: str, camera_id: str, camera_name: str):
        """处理调度模式下的摄像头流"""
        stream_key = f"scheduled_{camera_id}"
        logger.info(f"Starting scheduled stream processing: {camera_name} ({camera_id})")

        # 初始化流状态
        self.streams_status[stream_key] = {
            'status': 'initializing',
            'camera_name': camera_name,
            'camera_id': camera_id,
            'recorded_files_count': 0,
            'last_detection_time': None,
            'total_detections': 0,
            'should_stop': False
        }

        while not self.stop_event.is_set() and not self.streams_status[stream_key].get('should_stop', False):
            try:
                # 检查夜间模式
                if self._is_night_time():
                    logger.info(f"Night mode: pausing scheduled stream {camera_name}")
                    pause_duration = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('night_time', {}).get('pause_duration', 3600)
                    time.sleep(pause_duration)
                    continue

                # 处理流
                self._process_scheduled_stream_loop(stream_url, stream_key, camera_name, camera_id)

            except Exception as e:
                logger.error(f"Scheduled stream processing error for {camera_name}: {e}")
                time.sleep(30)  # 出错后等待30秒再重试

        # 清理状态
        if stream_key in self.streams_status:
            del self.streams_status[stream_key]
        if stream_key in self.stream_threads:
            del self.stream_threads[stream_key]

        logger.info(f"Scheduled stream processing stopped: {camera_name}")

    def _process_scheduled_stream_loop(self, stream_url: str, stream_key: str, camera_name: str, camera_id: str):
        """调度模式下的流处理主循环 - 回归简单OpenCV实现"""
        # 使用简单的OpenCV VideoCapture，参考老版本
        cap = cv2.VideoCapture(stream_url + '?transportmode=unicast', cv2.CAP_FFMPEG)

        if not cap.isOpened():
            logger.error(f"Failed to open scheduled stream: {camera_name}")
            time.sleep(60)  # 等待1分钟后重试
            return

        # 初始化变量（在try块之前，避免referenced before assignment错误）
        video_writer = None

        try:
            # 获取流信息
            fps = int(cap.get(cv2.CAP_PROP_FPS)) or 6  # 使用配置文件中的默认fps
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            logger.info(f"Scheduled stream opened: {camera_name} ({width}x{height}@{fps}fps)")

            # 更新状态
            self.streams_status[stream_key]['status'] = 'processing'

            # 处理参数 - 使用简单的固定值
            buffer_size = int(self.config.get('model', {}).get('vision', {}).get('stream', {}).get('recording', {}).get('buffer_size', 3) * fps)
            frame_buffer = deque(maxlen=buffer_size)
            skip_frames = 2  # 固定跳帧数

            frame_index = 0
            last_inference_frame = 0
            inference_interval = skip_frames + 1
            recording = False
            recording_start_frame = 0
            last_detection_frame = 0

            error_count = 0
            consecutive_failures = 0

            while not self.stop_event.is_set() and not self.streams_status[stream_key].get('should_stop', False):
                # 使用安全的帧读取
                ret, frame = self._safe_frame_read(cap, camera_name)

                if not ret or frame is None:
                    consecutive_failures += 1
                    logger.warning(f"Failed to read frame from scheduled stream: {camera_name} (failures: {consecutive_failures})")

                    # 如果连续失败次数过多，尝试重连
                    if consecutive_failures >= 5:
                        logger.warning(f"Too many consecutive failures for {camera_name}, attempting reconnection")
                        cap, error_count = self._handle_stream_error(cap, stream_url, camera_name, error_count)
                        consecutive_failures = 0

                        if cap is None or not cap.isOpened():
                            logger.error(f"Failed to reconnect to {camera_name}, stopping stream")
                            break
                        continue
                    else:
                        time.sleep(0.1)  # 短暂等待后继续
                        continue

                # 重置失败计数
                consecutive_failures = 0
                frame_index += 1
                frame_buffer.append((frame_index, frame.copy()))

                # 执行推理检测
                if frame_index - last_inference_frame >= inference_interval * fps:
                    last_inference_frame = frame_index

                    # 异步执行推理以避免阻塞
                    detection_result = self._detect_objects(frame)

                    if detection_result and len(detection_result.get('bboxes', [])) > 0:
                        last_detection_frame = frame_index
                        self.streams_status[stream_key]['last_detection_time'] = datetime.now()
                        self.streams_status[stream_key]['total_detections'] += len(detection_result['bboxes'])

                        logger.info(f"Detection in scheduled stream {camera_name}: {len(detection_result['bboxes'])} objects")

                        # 向调度器报告检测结果
                        if self.rotation_scheduler:
                            self.rotation_scheduler.report_detection(camera_id, len(detection_result['bboxes']))

                        # 开始录制
                        if not recording:
                            recording = True
                            recording_start_frame = frame_index
                            video_writer = self._start_recording_simple(stream_key, camera_name, fps, frame_buffer)

                # 录制逻辑
                if recording and video_writer:
                    video_writer.write(frame)

                    # 检查是否应该停止录制 - 参考老版本逻辑
                    recording_duration_frames = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('recording', {}).get('duration', 10) * fps
                    max_duration_frames = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('recording', {}).get('max_duration_sec', 30) * fps

                    frames_since_detection = frame_index - last_detection_frame
                    frames_since_start = frame_index - recording_start_frame

                    # 参考老版本的停止录制逻辑：duration或max_duration_sec任一条件满足即停止
                    if (frames_since_detection > recording_duration_frames or
                        frames_since_start > max_duration_frames):
                        recording = False
                        recording_start_frame = 0
                        if video_writer:
                            video_path = self._stop_recording(video_writer, stream_key, camera_name)
                            if video_path:
                                # 异步处理录制的视频
                                self._process_recorded_video(video_path, stream_url, stream_key)
                            video_writer = None

        finally:
            cap.release()
            if video_writer:
                video_path = self._stop_recording(video_writer, stream_key, camera_name)
                if video_path:
                    # 异步处理录制的视频
                    self._process_recorded_video(video_path, stream_url, stream_key)

    def stop_all_streams(self):
        """停止所有摄像头流"""
        logger.info("Stopping all camera streams...")

        # 停止轮转调度器
        if self.rotation_scheduler:
            self.rotation_scheduler.stop_scheduler()

        self.stop_event.set()

        # 等待所有线程结束
        for stream_key, thread in self.stream_threads.items():
            if thread.is_alive():
                thread.join(timeout=10)
                if thread.is_alive():
                    logger.warning(f"Stream thread {stream_key} did not stop gracefully")

        # 清理状态
        self.streams_status.clear()
        self.stream_threads.clear()
        self.paused_streams.clear()
        self.recording_buffers.clear()

        self.stop_event.clear()
        logger.info("All camera streams stopped")

    def get_streams_status(self) -> Dict[str, Any]:
        """获取流状态信息"""
        status = {
            'total_streams': len(self.streams_status),
            'active_streams': len([s for s in self.streams_status.values() if s['status'] == 'processing']),
            'paused_streams': len([s for s in self.streams_status.values() if s['status'] == 'paused']),
            'streams': self.streams_status.copy(),
            'rotation_enabled': self.rotation_scheduler is not None
        }

        # 如果启用了轮转调度，添加调度器状态
        if self.rotation_scheduler:
            status['rotation_status'] = self.rotation_scheduler.get_camera_status()

        return status

    def force_rotate_camera(self, camera_id: str) -> bool:
        """强制轮转指定摄像头"""
        if self.rotation_scheduler:
            return self.rotation_scheduler.force_rotate_camera(camera_id)
        return False

    def set_camera_priority(self, camera_id: str, priority: float):
        """设置摄像头优先级"""
        if self.rotation_scheduler:
            self.rotation_scheduler.set_camera_priority(camera_id, priority)

    def _process_single_stream(self, stream_url: str, stream_id: str, camera_name: str, count: int, is_pre_paused: bool):
        """处理单个摄像头流"""
        stream_key = f"{stream_url}_{stream_id}_{count}"
        logger.info(f"Starting stream processing: {camera_name} ({stream_id})")

        # 初始化流状态
        self.streams_status[stream_key] = {
            'status': 'paused' if is_pre_paused else 'initializing',
            'camera_name': camera_name,
            'stream_id': stream_id,
            'recorded_files_count': 0,
            'last_detection_time': None,
            'total_detections': 0
        }

        if is_pre_paused:
            self._pause_stream(stream_key, self.config['model']['vision']['stream']['pause_sec'])

        while not self.stop_event.is_set():
            try:
                # 检查是否处于暂停状态
                if self._is_stream_paused(stream_key):
                    time.sleep(1)
                    continue

                # 检查夜间模式
                if self._is_night_time():
                    logger.info(f"Night mode: pausing stream {camera_name}")
                    pause_duration = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('night_time', {}).get('pause_duration', 3600)
                    self._pause_stream(stream_key, pause_duration)
                    continue

                # 处理流
                self._process_stream_loop(stream_url, stream_key, camera_name)

            except Exception as e:
                logger.error(f"Stream processing error for {camera_name}: {e}")
                # 出错后暂停一段时间再重试
                retry_interval = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('retry', {}).get('interval_fail', 3600)
                self._pause_stream(stream_key, retry_interval)

        logger.info(f"Stream processing stopped: {camera_name}")

    def _process_stream_loop(self, stream_url: str, stream_key: str, camera_name: str):
        """流处理主循环 - 回归简单OpenCV实现，参考老版本逻辑"""
        # 使用简单的OpenCV VideoCapture，参考老版本
        cap = cv2.VideoCapture(stream_url + '?transportmode=unicast', cv2.CAP_FFMPEG)

        if not cap.isOpened():
            logger.error(f"Failed to open stream: {camera_name}")
            retry_interval = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('retry', {}).get('interval_fail', 3600)
            self._pause_stream(stream_key, retry_interval)
            return

        logger.info(f"Stream opened successfully: {camera_name}")
        self.streams_status[stream_key]['status'] = 'processing'

        # 流处理参数 - 参考老版本的参数计算方式
        fps = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('fps', 6)
        buffer_size = int(self.config.get('model', {}).get('vision', {}).get('stream', {}).get('recording', {}).get('buffer_size', 3) * fps)
        inference_interval_frames = int(self.config.get('model', {}).get('vision', {}).get('stream', {}).get('recording', {}).get('interval', 3) * fps)
        recording_duration_frames = int(self.config.get('model', {}).get('vision', {}).get('stream', {}).get('recording', {}).get('duration', 10) * fps)
        max_duration_frames = int(self.config.get('model', {}).get('vision', {}).get('stream', {}).get('recording', {}).get('max_duration_sec', 30) * fps)

        # 初始化变量 - 参考老版本的变量命名和逻辑
        frame_buffer = deque(maxlen=buffer_size)
        recording = False
        out = None
        recorded_files_count = 0

        frame_index = 0
        last_inference_frame_index = 0
        last_start_record_frame_index = None
        last_detect_frame_index = 0
        last_time_check = time.time()

        try:
            while not self.stop_event.is_set() and not self._is_stream_paused(stream_key):
                # 检查夜间时间 - 参考老版本逻辑
                current_time = time.time()
                if current_time - last_time_check > 1:
                    in_night = self._is_night_time()
                    last_time_check = current_time
                    if in_night:
                        logger.info(f"Night time pause: {camera_name}, current time: {datetime.now().time()}")
                        if recording:
                            logger.info(f"Night started, force stop recording: {camera_name}")
                            recording = False
                            recorded_files_count += 1
                            self.streams_status[stream_key]['recorded_files_count'] = recorded_files_count
                            last_start_record_frame_index = None

                            if out is not None:
                                video_path = self._stop_recording_simple(out, stream_key, camera_name)
                                if video_path:
                                    self._process_recorded_video(video_path, stream_url, stream_key)
                                out = None
                        # 暂停流直到白天
                        pause_duration = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('night_time', {}).get('pause_duration', 3600)
                        self._pause_stream(stream_key, pause_duration)
                        continue

                # 读取帧 - 使用简单的OpenCV读取
                ret, frame = cap.read()
                if not ret:
                    # 流结束处理 - 参考老版本逻辑
                    recording = False
                    if last_start_record_frame_index:
                        total_frames_recorded = frame_index - last_start_record_frame_index
                        total_duration_seconds = total_frames_recorded / fps
                        logger.info(f"Stop recording: {camera_name}, duration: {total_duration_seconds:.2f}s")
                        recorded_files_count += 1
                        self.streams_status[stream_key]['recorded_files_count'] = recorded_files_count
                        last_start_record_frame_index = None

                        if out is not None:
                            video_path = self._stop_recording_simple(out, stream_key, camera_name)
                            if video_path:
                                self._process_recorded_video(video_path, stream_url, stream_key)
                            out = None

                    retry_hours = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('pause_sec', 300) / 3600
                    logger.info(f"Stream ended: {camera_name}, will retry in {retry_hours:.2f} hours")
                    self._pause_stream(stream_key, self.config.get('model', {}).get('vision', {}).get('stream', {}).get('pause_sec', 300))
                    break

                frame_index += 1
                frame_buffer.append((frame, frame_index))

                # 检查队列大小限制 - 参考老版本逻辑
                active_streams = len([s for s in self.streams_status.values() if s['status'] == 'processing'])
                queue_size_limit = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('queue_size', 2)
                if active_streams > queue_size_limit:
                    logger.info(f"Too many active streams ({active_streams}), pausing: {camera_name}")
                    pause_duration = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('pause_sec', 300)
                    self._pause_stream(stream_key, pause_duration)
                    break

                # 写入录制帧 - 参考老版本的简单逻辑
                if recording and out is not None:
                    out.write(frame)

                # 执行推理检测 - 参考老版本的间隔逻辑
                if frame_index - last_inference_frame_index >= inference_interval_frames:
                    last_inference_frame_index = frame_index

                    # 执行推理
                    detection_result = self._detect_objects(frame)
                    bboxes = detection_result.get('bboxes', []) if detection_result else []

                    if len(bboxes) > 0:
                        last_detect_frame_index = frame_index
                        self.streams_status[stream_key]['last_detection_time'] = datetime.now()
                        self.streams_status[stream_key]['total_detections'] += len(bboxes)
                        logger.info(f"Detection in {camera_name}: {len(bboxes)} objects")

                    # 开始录制 - 参考老版本逻辑
                    if not recording and len(bboxes) > 0:
                        recording = True
                        last_start_record_frame_index = frame_index

                        # 使用简单的OpenCV VideoWriter - 参考老版本
                        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        output_file = f"{self.config['general']['tmp_path']}/{stream_key}_{timestamp}.mp4"

                        if frame_buffer:
                            sample_frame = frame_buffer[0][0]
                            frame_height, frame_width = sample_frame.shape[:2]
                            out = cv2.VideoWriter(
                                output_file,
                                fourcc,
                                fps,
                                (frame_width, frame_height)
                            )
                            # 添加filename属性以便后续使用
                            out.filename = output_file

                            # 写入缓冲区中的帧
                            for cached_frame, _ in frame_buffer:
                                out.write(cached_frame)

                            logger.info(f"Started recording: {camera_name} -> {output_file}")

                # 检查停止录制条件 - 参考老版本逻辑
                elif recording:
                    frames_since_detection = frame_index - last_detect_frame_index
                    frames_since_start = frame_index - last_start_record_frame_index if last_start_record_frame_index else 0

                    if (frames_since_detection > recording_duration_frames or
                        frames_since_start > max_duration_frames):
                        total_frames_recorded = frame_index - last_start_record_frame_index
                        total_duration_seconds = total_frames_recorded / fps
                        logger.info(f"Stop recording: {camera_name}, duration: {total_duration_seconds:.2f}s")
                        recording = False
                        recorded_files_count += 1
                        self.streams_status[stream_key]['recorded_files_count'] = recorded_files_count
                        last_start_record_frame_index = None

                        if out is not None:
                            video_path = self._stop_recording_simple(out, stream_key, camera_name)
                            if video_path:
                                self._process_recorded_video(video_path, stream_url, stream_key)
                            out = None

        except Exception as e:
            logger.error(f"Stream loop error for {camera_name}: {e}")
        finally:
            # 清理资源
            if out is not None:
                try:
                    out.release()
                except:
                    pass
            cap.release()
            self.streams_status[stream_key]['status'] = 'stopped'

    def _detect_objects(self, frame) -> Optional[Dict]:
        """检测帧中的对象"""
        try:
            return self.model_manager.predict_image(
                frame,
                model_type='bird_detection',
                imgsz=self.config.get('model', {}).get('vision', {}).get('stream', {}).get('size', 672),
                conf=self.config.get('model', {}).get('vision', {}).get('stream', {}).get('score_thr', 0.65)
            )
        except Exception as e:
            logger.error(f"Object detection failed: {e}")
            return None

    def _stop_recording_simple(self, video_writer, stream_key: str, camera_name: str) -> Optional[str]:
        """停止录制视频 - 简单版本，参考老版本逻辑"""
        try:
            if video_writer is None:
                return None

            # 获取输出文件路径
            if hasattr(video_writer, 'filename'):
                video_path = video_writer.filename
            else:
                # 如果没有filename属性，生成一个默认路径
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                video_path = f"{self.config['general']['tmp_path']}/{stream_key}_{timestamp}.mp4"

            # 释放写入器
            video_writer.release()

            # 检查文件是否存在且有内容
            if os.path.exists(video_path) and os.path.getsize(video_path) > 0:
                logger.info(f"Recording completed: {camera_name} -> {os.path.basename(video_path)}")
                return video_path
            else:
                logger.warning(f"Recording file is empty or missing: {os.path.basename(video_path)}")
                return None

        except Exception as e:
            logger.error(f"Failed to stop recording for {camera_name}: {e}")
            return None



    def _start_recording_simple(self, stream_key: str, camera_name: str, fps: float, frame_buffer: deque):
        """开始录制视频 - 简单版本，参考老版本逻辑"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{stream_key}_{timestamp}.mp4"
            output_path = os.path.join(self.config['general']['tmp_path'], filename)

            # 获取帧尺寸
            if frame_buffer:
                sample_frame = frame_buffer[0][0]  # 老版本格式：(frame, frame_index)
                height, width = sample_frame.shape[:2]
            else:
                width, height = 1920, 1080  # 默认尺寸

            # 使用简单的OpenCV VideoWriter - 参考老版本
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            if not video_writer.isOpened():
                logger.warning(f"Failed to open video writer for {camera_name}")
                return None

            # 写入缓冲区中的帧
            for buffered_frame, _ in frame_buffer:
                video_writer.write(buffered_frame)

            # 添加filename属性以便后续使用
            video_writer.filename = output_path
            logger.info(f"Started recording for {camera_name}: {filename}")
            return video_writer

        except Exception as e:
            logger.error(f"Failed to start recording for {camera_name}: {e}")
            return None



    def _process_recorded_video(self, video_path: str, stream_url: str, stream_key: str):
        """处理录制的视频"""
        try:
            # 提交视频处理任务
            job_id = queue_manager.submit_job('video', (video_path, stream_url, stream_key))
            logger.info(f"Submitted video processing job: {job_id} for {video_path}")
        except Exception as e:
            logger.error(f"Failed to submit video processing job: {e}")
            # 清理文件
            if os.path.exists(video_path):
                os.remove(video_path)

    def _is_stream_paused(self, stream_key: str) -> bool:
        """检查流是否处于暂停状态"""
        if stream_key not in self.paused_streams:
            return False

        pause_info = self.paused_streams[stream_key]
        if time.time() > pause_info.get('resume_time', 0):
            # 暂停时间结束，恢复流
            del self.paused_streams[stream_key]
            self.streams_status[stream_key]['status'] = 'processing'
            return False

        return True

    def _pause_stream(self, stream_key: str, duration_seconds: int):
        """暂停流指定时间"""
        self.paused_streams[stream_key] = {
            'resume_time': time.time() + duration_seconds,
            'duration': duration_seconds
        }
        self.streams_status[stream_key]['status'] = 'paused'

    def _is_night_time(self) -> bool:
        """检查是否为夜间时间（从配置文件读取）"""
        current_hour = datetime.now().hour
        night_start = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('night_time', {}).get('start_hour', 22)
        night_end = self.config.get('model', {}).get('vision', {}).get('stream', {}).get('night_time', {}).get('end_hour', 6)
        return current_hour >= night_start or current_hour <= night_end



# 先定义StreamProcessor类，然后再初始化组件
# （StreamProcessor类定义已经在后面，这里只是占位符）

# 现在初始化所有组件
video_processor = VideoProcessor(model_manager, file_processor, cfg)
# 初始化StreamProcessor
stream_processor = StreamProcessor(model_manager, file_processor, cfg)
worker_manager = WorkerManager(queue_manager, audio_processor, image_processor, video_processor, config)

# 启动工作线程
worker_manager.start_workers()

# 旧版本兼容的音频处理函数
def save_base64_to_tempfile(base64_data):
    """保存Base64数据为临时文件 - 兼容旧版本"""
    try:
        # 解码Base64数据
        audio_data = base64.b64decode(base64_data)

        # 创建临时文件（无扩展名）
        temp_file = tempfile.NamedTemporaryFile(delete=False, dir=cfg['general']['tmp_path'])
        temp_file.write(audio_data)
        temp_file.close()
        return temp_file.name
    except (base64.binascii.Error, ValueError) as e:
        raise ValueError(f"Base64 数据无效: {str(e)}")

def detect_and_convert_audio(file_path):
    """检测音频格式并转换为WAV - 兼容旧版本"""
    import filetype
    from pydub import AudioSegment

    kind = filetype.guess(file_path)
    if kind is None:
        raise ValueError("无法识别文件格式")

    if kind.mime == "audio/mpeg":
        try:
            audio = AudioSegment.from_mp3(file_path)
            wav_path = file_path + ".wav"
            audio.export(wav_path, format="wav")
            return wav_path
        except Exception as e:
            raise ValueError(f"MP3 转 WAV 失败: {str(e)}")
    elif kind.mime == "audio/x-wav":
        return file_path
    else:
        raise ValueError(f"不支持的音频格式: {kind.mime}")

# API端点
@app.route('/bird_sound_recognition_torch', methods=['POST'])
def bird_sound_recognition():
    """鸟类声音识别API - 完全兼容旧版本"""
    try:
        file = None
        file_id = request.form.get('fileID')

        # 处理Base64编码的音频 - 完全兼容旧版本
        if request.form.get('base64'):
            base64_audio = request.form.get('base64')
            try:
                # 保存Base64数据为临时文件
                temp_file_path = save_base64_to_tempfile(base64_audio)

                # 检测文件格式并转换为WAV
                wav_file_path = detect_and_convert_audio(temp_file_path)
            except ValueError as e:
                return jsonify({"error": str(e)}), 400, {'Content-Type': 'application/json'}
        else:
            # 处理文件上传 - 完全兼容旧版本
            if 'file' not in request.files:
                return jsonify({'error': '未上传文件'}), 400, {'Content-Type': 'application/json'}
            file = request.files['file']
            if file.filename == '':
                return jsonify({'error': '上传的文件为空'}), 400, {'Content-Type': 'application/json'}

            try:
                # 保存上传的文件
                file_extension = os.path.splitext(file.filename)[1]
                unique_filename = f"{uuid.uuid4()}{file_extension}"
                file_path = os.path.join(cfg['general']['tmp_path'], unique_filename)
                file.save(file_path)

                # 检测文件格式并转换为WAV
                wav_file_path = detect_and_convert_audio(file_path)
            except ValueError as e:
                return jsonify({"error": str(e)}), 400, {'Content-Type': 'application/json'}

        # 生成任务ID并加入队列 - 完全兼容旧版本
        job_id = str(uuid.uuid4())
        logger.info(f"[JOB] 音频任务: {job_id}")
        queue_manager.results_store[job_id] = JobResult(job_id=job_id, status='queued', cancelled=False)
        queue_manager.queues['audio'].put((job_id, wav_file_path, file_id))
        return jsonify({'job_id': job_id, 'status': 'queued'}), 200, {'Content-Type': 'application/json'}

    except Exception as e:
        logger.error(f"Audio recognition API error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/image_detection_torch', methods=['POST'])
def image_detection():
    """图像检测API - 完全兼容旧版本"""
    try:
        file_id = request.form.get('fileID')
        filename = f"{uuid.uuid4()}.jpg"  # 使用固定格式，与旧版本一致

        # 处理Base64编码的图像 - 完全兼容旧版本处理方式
        if request.form.get('base64'):
            base64_img = request.form.get('base64')
            try:
                import base64
                import io
                img_data = base64.b64decode(base64_img)
                file = io.BytesIO(img_data)
            except (base64.binascii.Error, ValueError) as e:
                return jsonify({"error": "Invalid base64 data", "details": str(e)}), 400, {'Content-Type': 'application/json'}

            file_path = os.path.join(cfg['general']['tmp_path'], filename)
            with open(file_path, 'wb') as f:
                f.write(file.getvalue())

        # 处理文件上传 - 完全兼容旧版本
        else:
            if 'file' not in request.files:
                return jsonify({'error': 'No file part'}), 400, {'Content-Type': 'application/json'}

            file = request.files['file']

            if file.filename == '' or not validate_file_type(file.filename, cfg['model']['vision']['img']['allow']):
                return jsonify({'error': 'Invalid file or no file uploaded'}), 400, {'Content-Type': 'application/json'}

            filename = secure_filename(file.filename)
            file_path = os.path.join(cfg['general']['tmp_path'], filename)
            file.save(file_path)

        # 生成任务ID并加入队列 - 完全兼容旧版本
        job_id = str(uuid.uuid4())
        logger.info(f"[JOB] Image job: {job_id}")
        queue_manager.results_store[job_id] = JobResult(job_id=job_id, status='queued', cancelled=False)
        # 添加默认的model_type参数以匹配工作线程期望的4个参数
        queue_manager.queues['image'].put((job_id, file_path, file_id, 'bird_detection'))

        return jsonify({
            'job_id': job_id,
            'status': 'queued'
        }), 200, {'Content-Type': 'application/json'}

    except Exception as e:
        logger.error(f"Image detection API error: {e}")
        return jsonify({'error': str(e)}), 500

# 内存清理任务
def memory_cleanup_task():
    """内存清理任务"""
    while True:
        try:
            # 检查内存使用情况
            metrics = monitor.get_system_metrics()

            # 降低内存清理阈值，更积极地清理
            memory_threshold = config.memory_limit_mb * 0.8  # 80%时开始清理

            if metrics['memory_mb'] > memory_threshold:
                logger.warning(f"Memory usage high: {metrics['memory_mb']:.1f}MB")

                # 清理已完成的任务 - 缩短清理时间到30分钟
                completed_jobs = [
                    job_id for job_id, job in queue_manager.results_store.items()
                    if job.status in ['completed', 'failed', 'cancelled']
                    and job.end_time
                    and (datetime.now() - job.end_time).total_seconds() > 1800  # 30分钟后清理
                ]

                for job_id in completed_jobs:
                    del queue_manager.results_store[job_id]

                logger.info(f"Cleaned {len(completed_jobs)} old jobs")

                # 清理临时文件
                import glob
                temp_files = glob.glob(os.path.join(cfg['general']['tmp_path'], '*'))
                current_time = time.time()
                for temp_file in temp_files:
                    try:
                        # 删除超过1小时的临时文件
                        if current_time - os.path.getmtime(temp_file) > 3600:
                            os.remove(temp_file)
                            logger.debug(f"Removed old temp file: {temp_file}")
                    except Exception as e:
                        logger.debug(f"Failed to remove temp file {temp_file}: {e}")

                # 强制垃圾回收
                import gc
                gc.collect()

                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

            time.sleep(config.memory_check_interval)

        except Exception as e:
            logger.error(f"Memory cleanup error: {e}")
            time.sleep(60)  # 出错时等待更长时间







# 流管理API
@app.route('/streams/status', methods=['GET'])
def get_streams_status():
    """获取所有流的状态"""
    try:
        status = stream_processor.get_streams_status()
        return jsonify(status), 200
    except Exception as e:
        logger.error(f"Get streams status error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/streams/start', methods=['POST'])
def start_streams():
    """启动所有摄像头流监控"""
    try:
        stream_processor.start_all_streams()
        return jsonify({'message': 'All streams started', 'status': 'success'}), 200
    except Exception as e:
        logger.error(f"Start streams error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/streams/stop', methods=['POST'])
def stop_streams():
    """停止所有摄像头流监控"""
    try:
        stream_processor.stop_all_streams()
        return jsonify({'message': 'All streams stopped', 'status': 'success'}), 200
    except Exception as e:
        logger.error(f"Stop streams error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/streams/restart', methods=['POST'])
def restart_streams():
    """重启所有摄像头流监控"""
    try:
        stream_processor.stop_all_streams()
        time.sleep(2)  # 等待清理完成
        stream_processor.start_all_streams()
        return jsonify({'message': 'All streams restarted', 'status': 'success'}), 200
    except Exception as e:
        logger.error(f"Restart streams error: {e}")
        return jsonify({'error': str(e)}), 500

# 旧版本兼容接口
@app.route('/get_streams_status', methods=['GET'])
def get_streams_status_legacy():
    """获取流状态 - 旧版本兼容接口"""
    try:
        # 返回旧版本格式的暂停流信息
        paused_streams_info = {}
        for stream_key, pause_info in stream_processor.paused_streams.items():
            paused_streams_info[stream_key] = {
                'pause_end_frame_index': pause_info.get('resume_time', 0),
                'status': 'paused',
                'rest_time': max(pause_info.get('resume_time', 0) - time.time(), 0)
            }

        return jsonify(paused_streams_info)
    except Exception as e:
        logger.error(f"Get legacy streams status error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/stop_all_streams', methods=['POST'])
def stop_all_streams_legacy():
    """停止所有流 - 旧版本兼容接口"""
    try:
        stream_processor.stop_all_streams()
        return jsonify({"message": "All streams stopped and resources cleaned"})
    except Exception as e:
        logger.error(f"Stop all streams legacy error: {e}")
        return jsonify({'error': str(e)}), 500

# 轮转调度管理API
@app.route('/rotation/status', methods=['GET'])
def get_rotation_status():
    """获取轮转调度状态"""
    try:
        if not stream_processor.rotation_scheduler:
            return jsonify({'error': 'Rotation scheduler not enabled'}), 400

        status = stream_processor.rotation_scheduler.get_camera_status()
        return jsonify(status), 200
    except Exception as e:
        logger.error(f"Get rotation status error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/rotation/force_rotate/<camera_id>', methods=['POST'])
def force_rotate_camera(camera_id: str):
    """强制轮转指定摄像头"""
    try:
        if not stream_processor.rotation_scheduler:
            return jsonify({'error': 'Rotation scheduler not enabled'}), 400

        success = stream_processor.force_rotate_camera(camera_id)
        if success:
            return jsonify({'message': f'Camera {camera_id} rotated successfully', 'status': 'success'}), 200
        else:
            return jsonify({'error': f'Camera {camera_id} not found or not active'}), 404
    except Exception as e:
        logger.error(f"Force rotate camera error: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/rotation/set_priority/<camera_id>', methods=['POST'])
def set_camera_priority(camera_id: str):
    """设置摄像头优先级"""
    try:
        if not stream_processor.rotation_scheduler:
            return jsonify({'error': 'Rotation scheduler not enabled'}), 400

        data = request.get_json()
        if not data or 'priority' not in data:
            return jsonify({'error': 'Priority value required'}), 400

        priority = float(data['priority'])
        if priority < 0 or priority > 10:
            return jsonify({'error': 'Priority must be between 0 and 10'}), 400

        stream_processor.set_camera_priority(camera_id, priority)
        return jsonify({'message': f'Priority set for camera {camera_id}', 'status': 'success'}), 200
    except ValueError:
        return jsonify({'error': 'Invalid priority value'}), 400
    except Exception as e:
        logger.error(f"Set camera priority error: {e}")
        return jsonify({'error': str(e)}), 500

# 自动启动摄像头流监控
def auto_start_streams():
    """自动启动摄像头流监控"""
    try:
        logger.info("Auto-starting camera streams...")
        time.sleep(5)  # 等待服务完全启动
        stream_processor.start_all_streams()
        logger.info("Camera streams auto-started successfully")
    except Exception as e:
        logger.error(f"Auto-start streams failed: {e}")

# 启动自动流监控线程
auto_start_thread = threading.Thread(target=auto_start_streams, daemon=True)
auto_start_thread.start()

# 启动内存清理线程
cleanup_thread = threading.Thread(target=memory_cleanup_task, daemon=True)
cleanup_thread.start()

if __name__ == '__main__':
    logger.info("Starting optimized bird recognition service...")

    try:
        # 启动服务
        server = pywsgi.WSGIServer(
            (cfg['general']['server']['host'], cfg['general']['server']['port']),
            app,
            log=logger
        )

        logger.info(f"Service started on {cfg['general']['server']['host']}:{cfg['general']['server']['port']}")
        server.serve_forever()

    except KeyboardInterrupt:
        logger.info("Service interrupted by user")
    except Exception as e:
        logger.error(f"Service failed: {e}")
    finally:
        # 停止所有流处理
        try:
            stream_processor.stop_all_streams()
        except:
            pass

        # 停止工作线程
        try:
            worker_manager.stop_workers()
        except:
            pass

        # 清理资源
        try:
            resource_manager.cleanup()
        except:
            pass

        logger.info("Service stopped")
