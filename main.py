import cv2
import numpy as np
import torch
from ultralytics import YOLO
import subprocess as sp
import logging

# 设置日志级别
logging.getLogger("ultralytics").setLevel(logging.CRITICAL)

# 检查设备
device = "cuda:0" if torch.cuda.is_available() else "cpu"

# 加载模型
model = YOLO("E:/python/接口参考和模型/模型/yolov12/best.pt")

# RTSP/FLV 流地址（可替换成你自己的）
rtmp_input_url = "http://*************:10000/proxy/sms/local/live/34020000001110000001_34020000001320000018.flv"
rtmp_output_url = 'rtmp://localhost/live/output'  # 输出推流地址，可以是本地或远程服务器

# 推流参数（根据你的需求修改）
width, height = 1920, 1080
fps = 25
command = ['ffmpeg',
           '-y',  # 覆盖输出文件
           '-f', 'rawvideo',  # 输入格式为原始视频
           '-pix_fmt', 'bgr24',  # 像素格式
           '-s', f'{width}x{height}',  # 分辨率
           '-r', str(fps),  # 帧率
           '-i', '-',  # 输入来自管道
           '-c:v', 'libx264',  # 编码器
           '-pix_fmt', 'yuv420p',  # 像素格式
           '-preset', 'ultrafast',
           '-f', 'flv',  # 输出格式
           rtmp_output_url]

# 启动 FFmpeg 子进程
process = sp.Popen(command, stdin=sp.PIPE)


def model_predict(frame):
    results = model.predict(source=frame,
                            imgsz=1536,
                            conf=0.5,
                            half=True,
                            device=device,
                            save=False,
                            show=False,
                            verbose=False)
    detections = []
    for box in results[0].boxes:
        x1, y1, x2, y2 = map(int, box.xyxy[0])
        width = x2 - x1
        height = y2 - y1
        conf = f"{box.conf[0]:.2f}"
        cls_id = int(box.cls[0])
        text = model.names[cls_id]
        detections.append({"box": [x1, y1, width, height], "conf": conf, "cls_id": cls_id, "text": text})
        # 在图像上绘制矩形框和标签
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        cv2.putText(frame, f'{text} {conf}', (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
    return frame


def main():
    cap = cv2.VideoCapture(rtmp_input_url)
    if not cap.isOpened():
        print("无法打开直播流")
        exit()

    while True:
        ret, frame = cap.read()
        if not ret:
            print("无法读取帧")
            break

        # 目标检测并绘制结果
        processed_frame = model_predict(frame)

        # 推流：写入到 FFmpeg 管道
        process.stdin.write(processed_frame.astype(np.uint8).tobytes())

    # 释放资源
    cap.release()
    process.stdin.close()
    process.terminate()
    process.wait()
    cv2.destroyAllWindows()


if __name__ == "__main__":
    main()