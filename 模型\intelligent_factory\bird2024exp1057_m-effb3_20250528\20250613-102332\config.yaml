model:
  type: models.TimmClassificationModel
  args:
    name: efficientnet_b3.ra2_in1k
    path: ckp/timm/efficientnet_b3.ra2_in1k/model.safetensors
    pretrained: false
    in_channels: 3
    global_pool: ''

trainer:
  type: utils.trainer.DDPTrainer
  exp_name: bird2024exp1057_m-effb3_20250528
  worldsize: 8
  max_epoch: 12
  accumulation_steps: 2
  ddp:
    master_addr: localhost
    master_port: '12355'
    backend: nccl
    find_unused_parameters: false

dataset:
  type: datasets.BirdCLEF_Dataset
  root: /home/<USER>/Works/transformers/data/changzhou_bird_soundfactory_20250528
  train_split: train
  val_split: validation
  trainloader:
    batchsize: 96
    shuffle: true
    num_workers: 2
    pin_memory: true
    drop_last: true
  valloader:
    batchsize: 8
    shuffle: false
    num_workers: 2
    pin_memory: true
    drop_last: false
  augmentation:
    epoch: -1
    noise: 0.0
    gain: 0.0
    wave_pitchshift: 0.0
    wave_shift: 0.0
    spec_xymasking: 0.5
    horizontal_cutmix: 0.5
    random_audio_seg: 0.5
  data:
    exts: [.wav, .mp3, .flac, .ogg, .m4a]
    train_duration: 10
    test_duration: 10
    sr: 32000
    fmin: 40
    fmax: 15000
    hop_length: 500
    n_mels: 128
    n_fft: 1024
    power: 2
    size_x: 512
    test_hop_length: 500

optim:
  type: torch.optim.AdamW
  lr: 0.003
  weight_decay: 0.01

lr_schedule:
  type: torch.optim.lr_scheduler.OneCycleLR
  pct_start: 0.0
  div_factor: 25
  final_div_factor: 0.4

loss_func:
  type: models.CrossEntropyFocalLoss
  alpha: 0.25
  gamma: 2.0
  reduction: mean

misc:
  seed: 42
  enable_amp: true
  deterministic: true

visualize:
  num_train_batch: 3
  num_val_batch: 9
